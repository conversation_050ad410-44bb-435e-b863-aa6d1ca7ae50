langchain-text-splitters==0.3.6
llama-cloud-services
fastembed==0.6.0
python-dotenv==1.0.1
qdrant_client==1.13.2
pydantic-ai==0.0.14
sec-api==1.0.32
openai>=1.0.0
streamlit>=1.28.0
qdrant-client>=1.6.0
requests>=2.28.0
sentence-transformers>=2.2.0
transformers>=4.21.0
torch>=1.12.0
numpy>=1.21.0
xhtml2pdf==0.2.16
pandas>=1.5.0
beautifulsoup4>=4.11.0
openpyxl==3.1.5
scikit-learn>=1.0.0
pymupdf>=1.23.0
faiss-cpu>=1.11.0

# FastAPI and dependencies
fastapi>=0.109.0
uvicorn[standard]>=0.27.0
pydantic>=2.5.0
pydantic-settings>=2.1.0
sqlalchemy>=2.0.0
alembic>=1.13.0
boto3>=1.34.0
python-multipart>=0.0.6

# PostgreSQL dependencies
asyncpg>=0.29.0
psycopg2-binary>=2.9.9
