#!/usr/bin/env python3
"""
Simple main function demonstrating the complete lattice retrieval workflow:
1. Get lattice headers using the actual extract_and_consolidate_lattice_headers function
2. Perform parallel retrieval + generation using those headers
"""

import logging
import os
import json
import asyncio
from typing import List
from dotenv import load_dotenv

load_dotenv()

# Import the actual lattice header extraction function
from agents_v2.query_engine.main import extract_and_consolidate_lattice_headers
from .document_retrieval import parallel_lattice_retrieval

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def main_async():
    """
    Async main function demonstrating the complete workflow.
    """
    
    print("🚀 PAYLOAD-BASED PARTITIONING LATTICE RETRIEVAL WORKFLOW")
    print("=" * 70)
    
    # Check prerequisites
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key:")
        print("export OPENAI_API_KEY='your_openai_api_key_here'")
        return
    
    if not os.getenv("QDRANT_API_KEY"):
        print("❌ Error: QDRANT_API_KEY environment variable not set") 
        print("Please set your Qdrant API key:")
        print("export QDRANT_API_KEY='your_qdrant_api_key_here'")
        return
    
    # Test Qdrant connection first
    print("\n🔧 Testing Qdrant connection...")
    try:
        from .document_retrieval import LatticeDocumentRetrieval
        retrieval_system = LatticeDocumentRetrieval()
        client = retrieval_system._get_qdrant_client()
        collections = client.get_collections()
        print(f"✅ Connected to Qdrant! Found {len(collections.collections)} collections")
    except Exception as e:
        print(f"❌ Failed to connect to Qdrant: {e}")
        print("Please run 'python test_qdrant_connection.py' to diagnose the issue")
        return
    
    # Configuration
    pdf_paths = [
        "mp_materials/pdfs/amendment.pdf",
        "mp_materials/pdfs/contract.pdf",
        "mp_materials/pdfs/exhibit-3.1.pdf",
        "mp_materials/pdfs/exhibit-10.7.pdf"
    ]
    
    collection_name = "document_chunks"
    k = 5  # Number of chunks to retrieve per query
    max_workers = 8  # Parallel workers
    
    try:
        # STEP 1: Extract lattice headers using the actual function
        print("\n📋 STEP 1: Extracting Lattice Headers")
        print("-" * 40)
        
        lattice_headers = await extract_and_consolidate_lattice_headers(
            pdf_paths=pdf_paths,
            extraction_count=20,  # Extract 20 headers per document
            final_count=10        # Consolidate to 15 final headers
        )
        
        if not lattice_headers:
            print("❌ No lattice headers were extracted. Check your documents and parsing setup.")
            return
        
        print(f"✅ Extracted {len(lattice_headers)} lattice headers:")
        for i, header in enumerate(lattice_headers[:10], 1):  # Show first 10
            print(f"   {i}. {header}")
        if len(lattice_headers) > 10:
            print(f"   ... and {len(lattice_headers) - 10} more")
        
        # STEP 2: Parallel retrieval + generation
        print(f"\n🔍 STEP 2: Parallel Retrieval + Generation")
        print("-" * 40)
        
        total_queries = len(lattice_headers) * len(pdf_paths)
        print(f"📊 Processing {len(lattice_headers)} headers × {len(pdf_paths)} documents = {total_queries} queries")
        
        results = parallel_lattice_retrieval(
            lattice_headers=lattice_headers,
            pdf_paths=pdf_paths,
            collection_name=collection_name,
            k=k,
            max_workers=max_workers
        )

        print("FINAL ANSWER!!!", results)
        
        # STEP 3: Process and display results
        print(f"\n📈 STEP 3: Results Analysis")
        print("-" * 40)
        
        # Calculate statistics
        total_queries_executed = 0
        successful_queries = 0
        high_confidence_queries = 0
        
        for header in results:
            for doc_name in results[header]:
                doc_result = results[header][doc_name]
                total_queries_executed += 1
                
                confidence = doc_result.get('confidence_score', 0.0)
                chunks_retrieved = doc_result.get('chunks_retrieved', 0)
                
                if confidence > 0.1 and chunks_retrieved > 0:
                    successful_queries += 1
                
                if confidence > 0.7:
                    high_confidence_queries += 1
        
        # Display summary
        print(f"✅ Total Queries: {total_queries_executed}")
        print(f"✅ Successful Queries: {successful_queries} ({successful_queries/total_queries_executed*100:.1f}%)")
        print(f"✅ High Confidence: {high_confidence_queries} ({high_confidence_queries/total_queries_executed*100:.1f}%)")
        
        # Show top results
        print(f"\n🎯 TOP HIGH-CONFIDENCE RESULTS:")
        print("-" * 40)
        
        top_results = []
        for header in results:
            for doc_name in results[header]:
                doc_result = results[header][doc_name]
                confidence = doc_result.get('confidence_score', 0.0)
                
                if confidence > 0.7:
                    top_results.append({
                        'header': header,
                        'document': os.path.basename(doc_name),
                        'confidence': confidence,
                        'answer': doc_result.get('answer', 'N/A')
                    })
        
        # Sort by confidence and show top 5
        top_results.sort(key=lambda x: x['confidence'], reverse=True)
        
        if top_results:
            for i, result in enumerate(top_results[:5], 1):
                print(f"\n{i}. 📋 {result['header']}")
                print(f"   📄 Document: {result['document']}")
                print(f"   🎯 Confidence: {result['confidence']:.2f}")
                
                # Truncate long answers
                answer = result['answer']
                if len(answer) > 150:
                    answer = answer[:150] + "..."
                print(f"   💬 Answer: {answer}")
        else:
            print("\n⚠️ No high-confidence results found. Try adjusting your lattice headers or document ingestion.")
        
        # Save results
        output_file = "simple_lattice_results.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Full results saved to: {output_file}")
        print("\n🎉 Payload-based partitioning workflow completed successfully!")
        print("✅ Used optimized tenant-based filtering for improved performance")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Error in workflow: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """
    Synchronous wrapper for the async main function.
    """
    return asyncio.run(main_async())


if __name__ == "__main__":
    main() 