#!/usr/bin/env python3
"""
Migration to remove unique constraint on bucket_key for dev sessions
This allows us to reuse the same S3 files across multiple sessions
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from api.models.database import SessionLocal
from sqlalchemy import text


def remove_bucket_key_unique():
    """Remove unique constraint on bucket_key column"""
    db = SessionLocal()
    
    try:
        print("🔧 Removing unique constraint on bucket_key...")
        
        # Drop the unique constraint
        db.execute(text("""
            ALTER TABLE uploaded_files 
            DROP CONSTRAINT IF EXISTS uploaded_files_bucket_key_key
        """))
        
        db.commit()
        print("✅ Successfully removed unique constraint on bucket_key")
        print("📝 Now multiple sessions can reference the same S3 files")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    print("Running migration to remove bucket_key unique constraint...")
    remove_bucket_key_unique() 