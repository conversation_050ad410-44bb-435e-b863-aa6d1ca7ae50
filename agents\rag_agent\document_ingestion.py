import logging
import traceback
import uuid
import os
import asyncio
import threading
from typing import List, <PERSON><PERSON>, Tuple
from dotenv import load_dotenv
import requests
import time
import numpy as np

from fastembed import SparseTextEmbedding  # Only need sparse embeddings from FastEmbed
from qdrant_client import QdrantClient
from qdrant_client.models import (
    VectorParams,
    Distance,
    SparseVectorParams,
    PointStruct,
    HnswConfigDiff,
    Modifier,
    KeywordIndexParams,
    SparseVector,
)
# from openai import OpenAI  # Not needed since we removed summary functionality
from langchain_text_splitters import RecursiveCharacterTextSplitter
from utils.pdf_to_markdown import convert_pdf_to_markdown_jina_full_document

load_dotenv()
logger = logging.getLogger(__name__)

# Jina Embeddings API Configuration
JINA_API_URL = "https://api.jina.ai/v1/embeddings"
JINA_API_KEY = os.getenv("JINA_API_KEY")


class DocumentIngestion:
    """Document ingestion class for processing and storing documents with embeddings in Qdrant Cloud.
    
    Uses Jina AI Reader for PDF parsing with intelligent caching and Jina embeddings for vector generation.
    Features:
    - Full document processing (not page-by-page) for better accuracy
    - Intelligent caching to avoid re-parsing unchanged documents
    - Jina embeddings v3 (1024 dimensions) for high-quality dense vectors
    - FastEmbed sparse embeddings for hybrid search capabilities
    - Configured to work with Qdrant Cloud instance using API key authentication
    - Thread-safe model initialization for concurrent usage
    """
    
    # Class-level locks for thread safety
    _sparse_model_lock = threading.Lock()
    _dense_model_lock = threading.Lock()
    
    def __init__(self):
        self._sparse_embedding_model = None
        self._dense_embedding_model = None
        # self._openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))  # Not needed anymore
        # self._memory_graph_url = "http://localhost:8000"  # Not needed anymore
    
    def init_sparse_embedding_model(self) -> None:
        """
        Initialize sparse embedding model during app startup.
        Thread-safe to prevent concurrent initialization conflicts with tqdm.
        """
        if self._sparse_embedding_model is None:
            with self._sparse_model_lock:
                # Double-check pattern to prevent race conditions
                if self._sparse_embedding_model is None:
                    try:
                        logger.info("Initializing sparse embedding model (thread-safe)...")
                        
                        # Initialize with specific settings to avoid tqdm conflicts
                        self._sparse_embedding_model = SparseTextEmbedding(
                            "Qdrant/bm25", 
                            cache_dir="model_cache",
                        )
                        logger.info("Sparse embedding model initialized successfully")
                    except Exception as e:
                        logger.error(f"Failed to initialize sparse embedding model: {str(e)}")
                        # Don't raise here, just log the error and continue
                        # This allows the system to continue working with dense embeddings only
                        self._sparse_embedding_model = "failed"  # Mark as failed

    def init_dense_embedding_model(self) -> None:
        """
        Initialize dense embedding model during app startup (using Jina API).
        Thread-safe to prevent concurrent initialization.
        """
        if self._dense_embedding_model is None:
            with self._dense_model_lock:
                # Double-check pattern to prevent race conditions
                if self._dense_embedding_model is None:
                    try:
                        logger.info("Initializing Jina dense embedding model (thread-safe)...")
                        
                        # Verify API key is available
                        if not JINA_API_KEY:
                            raise ValueError("JINA_API_KEY environment variable not set")
                        
                        # Test connection with a simple request
                        test_response = requests.post(
                            JINA_API_URL,
                            headers={
                                "Content-Type": "application/json",
                                "Authorization": f"Bearer {JINA_API_KEY}"
                            },
                            json={
                                "model": "jina-embeddings-v3",
                                "task": "text-matching",
                                "input": ["test"]
                            },
                            timeout=10
                        )
                        
                        if test_response.status_code == 200:
                            self._dense_embedding_model = "jina-embeddings-v3"  # Store model name
                            logger.info("Jina dense embedding model initialized successfully")
                        else:
                            raise RuntimeError(f"Jina API test failed: {test_response.status_code}")
                            
                    except Exception as e:
                        logger.error(f"Failed to initialize Jina dense embedding model: {str(e)}")
                        self._dense_embedding_model = "failed"  # Mark as failed

    async def pdf_to_text(self, file_paths, cache_dir="pdf_cache"):
        """
        Parse documents using Jina AI Reader API with caching and return the results.
        """
        jina_api_key = os.getenv("JINA_API_KEY")
        
        # Handle both single file and batch processing
        if isinstance(file_paths, str):
            # Single file
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, convert_pdf_to_markdown_jina_full_document, file_paths, jina_api_key, cache_dir
            )
            return result
        else:
            # Multiple files - process in parallel
            loop = asyncio.get_event_loop()
            tasks = []
            for file_path in file_paths:
                task = loop.run_in_executor(
                    None, convert_pdf_to_markdown_jina_full_document, file_path, jina_api_key, cache_dir
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            return results

    def parse_output(self, parse_results) -> Tuple[str, str]:
        """
        Get a simple tuple of (markdown, plain_text) from parsed documents.
        Handles both Jina format (markdown strings) and LlamaParse format (page objects).
        """
        # Handle single result (not a list)
        if isinstance(parse_results, str):
            # Jina returns markdown directly
            markdown_content = parse_results
            # For text content, we'll use the same markdown (Jina already extracts clean text)
            text_content = parse_results
            return (markdown_content, text_content)
        
        # Handle list of results
        if not isinstance(parse_results, list):
            parse_results = [parse_results]
        
        markdown_parts = []
        text_parts = []
        
        for result in parse_results:
            if isinstance(result, str):
                # Jina format - result is already markdown
                markdown_parts.append(result)
                text_parts.append(result)
            elif hasattr(result, 'pages'):
                # LlamaParse format - result has pages
                for page in result.pages:
                    if hasattr(page, 'md'):
                        markdown_parts.append(f"{page.md}")
                    if hasattr(page, 'text'):
                        text_parts.append(f"{page.text}")
            else:
                # Fallback - treat as string
                markdown_parts.append(str(result))
                text_parts.append(str(result))
        
        markdown_content = '\n\n---\n\n'.join(markdown_parts)
        text_content = ('\n\n' + '='*50 + '\n\n').join(text_parts)
        
        return (markdown_content, text_content)

    # def create_summary(self, text: str) -> str:
    #     """
    #     Create a summary of the text.
    #     """
    #     response = self._openai_client.chat.completions.create(
    #         model="gpt-4o",
    #         messages=[
    #             {"role": "system", "content": "You are a skilled financial analyst. Analyze the financial document and provide a clear summary covering key financial performance, position, risks, facts, figures and important metrics with specific numbers. Focus on revenue trends, profitability, balance sheet strength, and cash flow. Cover all the key information in the document."},
    #             {"role": "user", "content": text}
    #         ]
    #     )
    #     return response.choices[0].message.content

    def _chunk_text(self, text: str, chunk_size: int = 1280, chunk_overlap: int = 256) -> List[str]:
        """
        Split text into chunks using LangChain's RecursiveCharacterTextSplitter.
        """
        start_time = time.time()
        
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=[],
        )
        chunks = text_splitter.split_text(text)
        
        duration = time.time() - start_time
        avg_chunk_size = sum(len(chunk) for chunk in chunks) / len(chunks) if chunks else 0
        logger.debug(f"Text chunked in {duration:.3f}s: {len(text):,} chars → {len(chunks)} chunks (avg: {avg_chunk_size:.0f} chars/chunk)")
        
        return chunks

    # def ingest_summaries_to_graph(self, summary: str, doc_id: str, user_id: str, doc_name: str) -> bool:
    #     """
    #     Ingest document summaries to the memory graph via API call.
    #     """
    #     try:
    #         logger.info("Ingesting summary to memory graph...")
    #         
    #         # Prepare the payload
    #         payload = {
    #             "content": summary,
    #             "metadata": {
    #                 "labels": [],
    #                 "properties": [
    #                     {"doc_id": doc_id},
    #                     {"user_id": user_id},
    #                     {"doc_name": doc_name}
    #                 ]
    #             }
    #         }
    #         
    #         # Make the API call to the memory graph insert endpoint
    #         response = requests.post(
    #             f"{self._memory_graph_url}/api/insert",
    #             json=payload,
    #             headers={"Content-Type": "application/json"},
    #             timeout=30
    #         )
    #         
    #         # Check if the request was successful
    #         if response.status_code in [200, 202]:
    #             if response.status_code == 200:
    #                 logger.info("Successfully ingested summary to memory graph")
    #             elif response.status_code == 202:
    #                 response_data = response.json()
    #                 task_id = response_data.get('task_id', 'unknown')
    #                 logger.info(f"Successfully queued summary for memory graph processing (Task ID: {task_id})")
    #             return True
    #         else:
    #             logger.error(f"Failed to ingest summary to memory graph. Status code: {response.status_code}, Response: {response.text}")
    #             return False
    #             
    #     except requests.exceptions.ConnectionError:
    #         logger.error("Failed to connect to memory graph API. Make sure the service is running on localhost:8000")
    #         return False
    #     except requests.exceptions.Timeout:
    #         logger.error("Request to memory graph API timed out")
    #         return False
    #     except Exception as e:
    #         logger.error(f"Error ingesting summary to memory graph: {str(e)}")
    #         logger.debug(f"Memory graph ingestion traceback: {traceback.format_exc()}")
    #         return False

    def get_sparse_embeddings(self, text: str) -> Optional[SparseVector]:
        """
        Get sparse embeddings for a single text string using the sparse model.
        """
        if self._sparse_embedding_model is None:
            logger.error("Sparse embedding model not initialized. Call init_sparse_embedding_model() first.")
            return None
        
        if self._sparse_embedding_model == "failed":
            logger.warning("Sparse embedding model initialization failed. Skipping sparse embeddings.")
            return None
        
        start_time = time.time()
        try:
            # Generate sparse embeddings
            sparse_embeddings = list(self._sparse_embedding_model.embed([text]))
            if sparse_embeddings:
                sparse_emb = sparse_embeddings[0]
                duration = time.time() - start_time
                # Convert SparseEmbedding to SparseVector format
                result = SparseVector(
                    indices=sparse_emb.indices.tolist(),
                    values=sparse_emb.values.tolist()
                )
                logger.debug(f"Sparse embedding generated in {duration:.3f}s for {len(text)} chars ({len(result.indices)} features)")
                return result
            return None
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Sparse embedding generation failed after {duration:.3f}s: {str(e)}")
            logger.debug(f"Sparse embedding traceback: {traceback.format_exc()}")
            return None

    def get_dense_embeddings(self, text: str) -> Optional[List[float]]:
        """
        Get dense embeddings for a single text string using Jina API.
        """
        if self._dense_embedding_model is None:
            logger.error("Dense embedding model not initialized. Call init_dense_embedding_model() first.")
            return None
        
        if self._dense_embedding_model == "failed":
            logger.error("Dense embedding model initialization failed. Cannot generate embeddings.")
            return None
        
        start_time = time.time()
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {JINA_API_KEY}"
            }
            
            data = {
                "model": "jina-embeddings-v3",
                "task": "text-matching",
                "input": [text]
            }
            
            response = requests.post(JINA_API_URL, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                embeddings = result["data"]
                if embeddings:
                    duration = time.time() - start_time
                    logger.debug(f"Dense embedding generated in {duration:.2f}s for {len(text)} chars")
                    return embeddings[0]["embedding"]
                return None
            else:
                logger.error(f"Jina API request failed with status {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Dense embedding generation failed after {duration:.2f}s: {str(e)}")
            logger.debug(f"Dense embedding traceback: {traceback.format_exc()}")
            return None

    def initialize_qdrant_collection(
        self,
        collection_name: str, 
        qdrant_client: QdrantClient,
        dense_vector_size: int = 1024  # Jina embeddings v3 has 1024 dimensions
    ) -> bool:
        """
        Initialize Qdrant collection if it doesn't exist.
        """
        try:
            logger.info(f"Initializing Qdrant collection: {collection_name}")
            
            # Check if collection exists
            collections = qdrant_client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if collection_name not in collection_names:
                qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config={
                        "dense": VectorParams(
                            size=dense_vector_size, 
                            distance=Distance.COSINE
                        )
                    },
                    sparse_vectors_config={
                        "sparse": SparseVectorParams(
                            modifier=Modifier.IDF,
                        )
                    },
                    hnsw_config=HnswConfigDiff(
                        payload_m=16,  # Configure local index for payload-based filtering
                        m=0,  # Disable global index
                    ),
                )
                
                # Create index for the user_id field to optimize user-based queries
                qdrant_client.create_payload_index(
                    collection_name=collection_name,
                    field_name="user_id",
                    field_schema=KeywordIndexParams(
                        type="keyword",
                        is_tenant=True,  # Optimize for tenant-based queries
                    ),
                )
                logger.info(f"Collection '{collection_name}' created successfully.")
            else:
                logger.info(f"Collection '{collection_name}' already exists.")

            return True
        except Exception as e:
            logger.error(f"Failed to initialize collection '{collection_name}': {str(e)}")
            logger.debug(f"Collection initialization traceback: {traceback.format_exc()}")
            return False

    def ingest_document_to_qdrant(
        self,
        text: str,
        doc_id: str,
        user_id: str,
        doc_name: str,
        collection_name: str,
        qdrant_client: QdrantClient,
        chunk_size: int = 1280,
        chunk_overlap: int = 256
    ) -> bool:
        """
        Ingest a document into Qdrant database by chunking and creating embeddings.
        """
        try:
            logger.info(f"Ingesting document {doc_id} for user {user_id}")
            
            # Split text into chunks
            chunks = self._chunk_text(text, chunk_size, chunk_overlap)
            
            # Get embeddings for all chunks
            points = []
            
            for chunk in chunks:
                # Get dense embeddings
                dense_embedding = self.get_dense_embeddings(chunk)
                if not dense_embedding:
                    logger.error(f"Failed to get dense embeddings for chunk in document {doc_id}")
                    continue
                
                # Get sparse embeddings
                sparse_embedding = self.get_sparse_embeddings(chunk)
                if not sparse_embedding:
                    logger.error(f"Failed to get sparse embeddings for chunk in document {doc_id}")
                    continue
                
                # Create point
                points.append(
                    PointStruct(
                        id=str(uuid.uuid4()),
                        vector={
                            "dense": dense_embedding,
                            "sparse": sparse_embedding,
                        },
                        payload={
                            "doc_id": doc_id,
                            "user_id": user_id,
                            "doc_name": doc_name,
                            "text": chunk,
                        },
                    )
                )
            
            if not points:
                logger.error(f"No valid points created for document {doc_id}")
                return False
            
            # Upload all points to Qdrant in smaller batches to avoid timeouts
            upsert_start_time = time.time()
            logger.info(f"Upserting {len(points)} points for document {doc_id} in batches")
            
            batch_size = 50  # Smaller batches to avoid timeouts
            total_batches = (len(points) + batch_size - 1) // batch_size
            successful_batches = 0
            
            for i in range(0, len(points), batch_size):
                batch_start_time = time.time()
                batch_points = points[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                try:
                    qdrant_client.upsert(collection_name=collection_name, points=batch_points)
                    batch_duration = time.time() - batch_start_time
                    logger.debug(f"Upserted batch {batch_num}/{total_batches} in {batch_duration:.2f}s ({len(batch_points)} points)")
                    successful_batches += 1
                except Exception as e:
                    batch_duration = time.time() - batch_start_time
                    logger.error(f"Failed to upsert batch {batch_num}/{total_batches} after {batch_duration:.2f}s: {str(e)}")
            
            upsert_duration = time.time() - upsert_start_time
            points_per_sec = len(points) / upsert_duration if upsert_duration > 0 else 0
            
            if successful_batches == total_batches:
                logger.info(f"Successfully upserted all {len(points)} points in {upsert_duration:.2f}s ({points_per_sec:.0f} points/sec)")
                return True
            else:
                logger.error(f"Only {successful_batches}/{total_batches} batches successful in {upsert_duration:.2f}s")
                return False
            
        except Exception as e:
            logger.error(f"Error ingesting document {doc_id}: {str(e)}")
            logger.debug(f"Document ingestion traceback: {traceback.format_exc()}")
            return False

    def get_dense_embeddings_batch(self, texts: List[str], batch_size: int = 100, max_retries: int = 3) -> Optional[List[List[float]]]:
        """
        Get dense embeddings for multiple texts efficiently in batch using Jina API.
        """
        if self._dense_embedding_model is None:
            logger.error("Dense embedding model not initialized. Call init_dense_embedding_model() first.")
            return None
        
        total_start_time = time.time()
        total_chars = sum(len(text) for text in texts)
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {JINA_API_KEY}"
            }
            
            all_embeddings = []
            total_batches = (len(texts) + batch_size - 1) // batch_size
            
            logger.info(f"Processing {len(texts)} texts ({total_chars:,} chars) in {total_batches} batches")
            
            for i in range(0, len(texts), batch_size):
                batch_start_time = time.time()
                batch_texts = texts[i:i + batch_size]
                batch_num = i // batch_size + 1
                batch_chars = sum(len(text) for text in batch_texts)
                
                logger.debug(f"Processing batch {batch_num}/{total_batches} ({len(batch_texts)} texts, {batch_chars:,} chars)")
                
                # Retry mechanism for API calls
                delay = 2
                for attempt in range(max_retries):
                    try:
                        data = {
                            "model": "jina-embeddings-v3",
                            "task": "text-matching",
                            "input": batch_texts
                        }
                        
                        api_start_time = time.time()
                        response = requests.post(JINA_API_URL, headers=headers, json=data, timeout=60)
                        api_duration = time.time() - api_start_time
                        
                        if response.status_code == 200:
                            batch_result = response.json()
                            batch_embeddings = batch_result["data"]
                            all_embeddings.extend([e["embedding"] for e in batch_embeddings])
                            
                            batch_duration = time.time() - batch_start_time
                            logger.debug(f"Batch {batch_num}/{total_batches} completed in {batch_duration:.2f}s (API: {api_duration:.2f}s)")
                            break
                        else:
                            logger.warning(f"API request failed with status {response.status_code} after {api_duration:.2f}s")
                            if attempt < max_retries - 1:
                                logger.info(f"Retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})")
                                time.sleep(delay)
                                delay *= 2
                            else:
                                raise RuntimeError(f"Jina API failed after {max_retries} attempts: {response.text}")
                                
                    except Exception as e:
                        if attempt < max_retries - 1:
                            logger.warning(f"Exception occurred: {e}")
                            logger.info(f"Retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})")
                            time.sleep(delay)
                            delay *= 2
                        else:
                            raise
            
            total_duration = time.time() - total_start_time
            chars_per_sec = total_chars / total_duration if total_duration > 0 else 0
            logger.info(f"Batch embeddings completed in {total_duration:.2f}s ({len(all_embeddings)} embeddings, {chars_per_sec:,.0f} chars/sec)")
            return all_embeddings
            
        except Exception as e:
            total_duration = time.time() - total_start_time
            logger.error(f"Batch dense embedding generation failed after {total_duration:.2f}s: {str(e)}")
            logger.debug(f"Batch dense embedding traceback: {traceback.format_exc()}")
            return None

    def get_sparse_embeddings_batch(self, texts: List[str]) -> Optional[List[SparseVector]]:
        """
        Get sparse embeddings for multiple texts efficiently in batch.
        """
        if self._sparse_embedding_model is None:
            logger.error("Sparse embedding model not initialized. Call init_sparse_embedding_model() first.")
            return None
        
        start_time = time.time()
        total_chars = sum(len(text) for text in texts)
        
        try:
            # Generate sparse embeddings
            sparse_embeddings = list(self._sparse_embedding_model.embed(texts))
            
            # Convert each SparseEmbedding to SparseVector format
            results = [
                SparseVector(
                    indices=sparse_emb.indices.tolist(),
                    values=sparse_emb.values.tolist()
                )
                for sparse_emb in sparse_embeddings
            ]
            
            duration = time.time() - start_time
            total_features = sum(len(result.indices) for result in results)
            chars_per_sec = total_chars / duration if duration > 0 else 0
            
            logger.debug(f"Sparse embeddings batch completed in {duration:.3f}s ({len(results)} embeddings, {total_features:,} features, {chars_per_sec:,.0f} chars/sec)")
            return results
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Batch sparse embedding generation failed after {duration:.3f}s: {str(e)}")
            logger.debug(f"Batch sparse embedding traceback: {traceback.format_exc()}")
            return None
        
    
    
async def run_ingestion_pipeline(
    file_path: str,
    doc_id: str = None,
    user_id: str = "default_user",
    collection_name: str = "document_chunks",
    qdrant_url: str = "https://3312ccd1-6af5-4b15-8e3e-60ddbdd7b38a.europe-west3-0.gcp.cloud.qdrant.io:6333/",
    chunk_size: int = 1280,
    chunk_overlap: int = 256,
    cache_dir: str = "pdf_cache"
) -> Tuple[bool, bool]:
    """
    Complete document ingestion pipeline from PDF to vector storage with caching and Jina embeddings.
    
    Args:
        file_path: Path to the PDF file to process
        doc_id: Unique document identifier (auto-generated if None)
        user_id: User identifier for the document
        collection_name: Qdrant collection name
        qdrant_url: Qdrant database URL
        chunk_size: Size of text chunks for vector storage
        chunk_overlap: Overlap between chunks
        cache_dir: Directory to store cached parsing results
        
    Returns:
        Tuple of (qdrant_success, placeholder_boolean) - second value is always True
    """
    pipeline_start_time = time.time()
    
    try:
        logger.info(f"🚀 Starting document ingestion pipeline for: {file_path}")
        
        # Initialize document ingestion class
        init_start_time = time.time()
        doc_ingestion = DocumentIngestion()
        
        # Initialize embedding models
        doc_ingestion.init_sparse_embedding_model()
        doc_ingestion.init_dense_embedding_model()
        init_duration = time.time() - init_start_time
        
        # Initialize Qdrant client with API key for cloud instance and timeout settings
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        if qdrant_api_key:
            qdrant_client = QdrantClient(
                url=qdrant_url, 
                api_key=qdrant_api_key,
                timeout=120,  # 2 minutes timeout for cloud operations
                prefer_grpc=False  # Use HTTP for better timeout control
            )
        else:
            qdrant_client = QdrantClient(
                url=qdrant_url,
                timeout=120,  # 2 minutes timeout
                prefer_grpc=False  # Use HTTP for better timeout control
            )
        
        # Initialize Qdrant collection
        collection_success = doc_ingestion.initialize_qdrant_collection(
            collection_name=collection_name,
            qdrant_client=qdrant_client
        )
        
        if not collection_success:
            logger.error("Failed to initialize Qdrant collection")
            return False, False
        
        # Generate doc_id if not provided
        if doc_id is None:
            doc_id = str(uuid.uuid4())
        
        logger.info(f"⚙️ Initialization completed in {init_duration:.2f}s")
        
        # Step 1: Parse PDF to text (with caching)
        parse_start_time = time.time()
        logger.info("📄 Step 1: Parsing PDF to text...")
        parse_results = await doc_ingestion.pdf_to_text(file_path, cache_dir)
        parse_duration = time.time() - parse_start_time
        
        # Step 2: Parse output to get plain text
        extract_start_time = time.time()
        logger.info("📝 Step 2: Extracting plain text...")
        markdown, plain_text = doc_ingestion.parse_output(parse_results)
        extract_duration = time.time() - extract_start_time
        
        if not plain_text.strip():
            logger.error("No text extracted from PDF")
            return False, False
        
        logger.info(f"📄 PDF parsing completed in {parse_duration:.2f}s, extracted {len(plain_text):,} characters")
        
        # Step 3: Ingest document chunks to Qdrant
        ingest_start_time = time.time()
        logger.info("🔄 Step 3: Ingesting document chunks to Qdrant...")
        qdrant_success = doc_ingestion.ingest_document_to_qdrant(
            text=plain_text,
            doc_id=doc_id,
            user_id=user_id,
            doc_name=file_path,
            collection_name=collection_name,
            qdrant_client=qdrant_client,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        ingest_duration = time.time() - ingest_start_time
        
        # Log results
        pipeline_duration = time.time() - pipeline_start_time
        logger.info(f"🎉 Pipeline completed in {pipeline_duration:.2f}s (init: {init_duration:.2f}s, parse: {parse_duration:.2f}s, ingest: {ingest_duration:.2f}s) - Qdrant: {'Success' if qdrant_success else 'Failed'}")
        
        return qdrant_success, True  # Return True for graph_success since we're not using it
        
    except Exception as e:
        pipeline_duration = time.time() - pipeline_start_time
        logger.error(f"❌ Error in document ingestion pipeline after {pipeline_duration:.2f}s: {str(e)}")
        logger.debug(f"Pipeline traceback: {traceback.format_exc()}")
        return False, False


def run_ingestion_pipeline_sync(file_path: str, **kwargs) -> Tuple[bool, bool]:
    """
    Synchronous wrapper for the async run_ingestion_pipeline function with caching.
    
    Args:
        file_path: Path to the PDF file to process
        **kwargs: Additional arguments passed to run_ingestion_pipeline (including cache_dir)
        
    Returns:
        Tuple of (qdrant_success, placeholder_boolean) - second value is always True
    """
    return asyncio.run(run_ingestion_pipeline(file_path, **kwargs))


async def run_batch_ingestion_pipeline(
    file_paths: List[str],
    doc_ids: List[str] = None,
    user_id: str = "default_user",
    collection_name: str = "document_chunks",
    qdrant_url: str = "https://3312ccd1-6af5-4b15-8e3e-60ddbdd7b38a.europe-west3-0.gcp.cloud.qdrant.io:6333/",
    chunk_size: int = 1280,
    chunk_overlap: int = 256,
    cache_dir: str = "pdf_cache"
) -> List[Tuple[str, bool, bool]]:
    """
    Complete batch document ingestion pipeline for processing multiple PDFs in parallel with caching and Jina embeddings.
    
    Args:
        file_paths: List of paths to PDF files to process
        doc_ids: List of unique document identifiers (auto-generated if None)
        user_id: User identifier for the documents
        collection_name: Qdrant collection name
        qdrant_url: Qdrant database URL
        chunk_size: Size of text chunks for vector storage
        chunk_overlap: Overlap between chunks
        cache_dir: Directory to store cached parsing results
        
    Returns:
        List of tuples (file_path, qdrant_success, placeholder_boolean) for each document
    """
    batch_start_time = time.time()
    
    try:
        total_files = len(file_paths)
        logger.info(f"🚀 Starting batch document ingestion pipeline for {total_files} documents")
        
        # Initialize document ingestion class
        init_start_time = time.time()
        doc_ingestion = DocumentIngestion()
        
        # Initialize embedding models
        doc_ingestion.init_sparse_embedding_model()
        doc_ingestion.init_dense_embedding_model()
        init_duration = time.time() - init_start_time
        
        # Initialize Qdrant client with API key for cloud instance and timeout settings
        qdrant_api_key = os.getenv("QDRANT_API_KEY")
        if qdrant_api_key:
            qdrant_client = QdrantClient(
                url=qdrant_url, 
                api_key=qdrant_api_key,
                timeout=120,  # 2 minutes timeout for cloud operations
                prefer_grpc=False  # Use HTTP for better timeout control
            )
        else:
            qdrant_client = QdrantClient(
                url=qdrant_url,
                timeout=120,  # 2 minutes timeout
                prefer_grpc=False  # Use HTTP for better timeout control
            )
        
        # Initialize Qdrant collection
        collection_success = doc_ingestion.initialize_qdrant_collection(
            collection_name=collection_name,
            qdrant_client=qdrant_client
        )
        
        if not collection_success:
            logger.error("Failed to initialize Qdrant collection")
            return [(fp, False, False) for fp in file_paths]
        
        # Generate doc_ids if not provided
        if doc_ids is None:
            doc_ids = [str(uuid.uuid4()) for _ in file_paths]
        elif len(doc_ids) != len(file_paths):
            raise ValueError("Length of doc_ids must match length of file_paths")
        
        logger.info(f"⚙️ Batch initialization completed in {init_duration:.2f}s")
        
        # Step 1: Parse all PDFs in parallel using batch processing (with caching)
        parse_start_time = time.time()
        logger.info("📄 Step 1: Parsing all PDFs to text in parallel...")
        parse_results = await doc_ingestion.pdf_to_text(file_paths, cache_dir)
        parse_duration = time.time() - parse_start_time
        
        # Ensure parse_results is always a list
        if not isinstance(parse_results, list):
            parse_results = [parse_results]
        
        logger.info(f"📄 Batch PDF parsing completed in {parse_duration:.2f}s")
        
        # Step 2: Process each document in parallel
        process_start_time = time.time()
        logger.info("🔄 Step 2: Processing all documents in parallel...")
        
        # Create tasks for parallel processing of each document
        tasks = []
        for i, file_path in enumerate(file_paths):
            task = _process_single_document_async(
                doc_ingestion=doc_ingestion,
                parse_result=parse_results[i] if i < len(parse_results) else [],
                file_path=file_path,
                doc_id=doc_ids[i],
                user_id=user_id,
                collection_name=collection_name,
                qdrant_client=qdrant_client,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap
            )
            tasks.append(task)
        
        # Execute all document processing tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        process_duration = time.time() - process_start_time
        
        # Process results and handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Error processing document {file_paths[i]}: {str(result)}")
                final_results.append((file_paths[i], False, False))
            else:
                final_results.append((file_paths[i], result[0], result[1]))
        
        # Log overall results
        batch_duration = time.time() - batch_start_time
        successful_qdrant = sum(1 for _, q, _ in final_results if q)
        docs_per_sec = total_files / batch_duration if batch_duration > 0 else 0
        
        logger.info(f"🎉 Batch pipeline completed in {batch_duration:.2f}s (init: {init_duration:.2f}s, parse: {parse_duration:.2f}s, process: {process_duration:.2f}s) - Success: {successful_qdrant}/{total_files} ({docs_per_sec:.1f} docs/sec)")
        
        return final_results
        
    except Exception as e:
        batch_duration = time.time() - batch_start_time
        logger.error(f"❌ Error in batch document ingestion pipeline after {batch_duration:.2f}s: {str(e)}")
        logger.debug(f"Batch pipeline traceback: {traceback.format_exc()}")
        return [(fp, False, False) for fp in file_paths]


async def _process_single_document_async(
    doc_ingestion: 'DocumentIngestion',
    parse_result,
    file_path: str,
    doc_id: str,
    user_id: str,
    collection_name: str,
    qdrant_client: QdrantClient,
    chunk_size: int,
    chunk_overlap: int
) -> Tuple[bool, bool]:
    """
    Process a single document asynchronously as part of batch processing.
    
    Args:
        parse_result: Can be a string (Jina format) or list (LlamaParse format)
    
    Returns:
        Tuple of (qdrant_success, graph_success) booleans
    """
    doc_start_time = time.time()
    
    try:
        logger.info(f"📄 Processing document: {file_path}")
        
        # Parse output to get plain text
        extract_start_time = time.time()
        markdown, plain_text = doc_ingestion.parse_output(parse_result)
        extract_duration = time.time() - extract_start_time
        
        if not plain_text.strip():
            logger.error(f"No text extracted from PDF: {file_path}")
            return False, False
        
        logger.debug(f"Text extraction completed in {extract_duration:.3f}s for {file_path} ({len(plain_text):,} chars)")
        
        # Create task for chunking and embedding
        points = await _chunk_and_embed_async(
            doc_ingestion, plain_text, doc_id, user_id, file_path, chunk_size, chunk_overlap
        )
        
        # Upsert points to Qdrant with chunking to avoid timeouts
        qdrant_success = await _upsert_points_async(
            qdrant_client, collection_name, points, doc_id, 
            batch_size=50,  # Process in smaller batches to avoid timeouts
            max_retries=3
        )
        
        doc_duration = time.time() - doc_start_time
        chars_per_sec = len(plain_text) / doc_duration if doc_duration > 0 else 0
        
        logger.info(f"📋 Document {file_path} processed in {doc_duration:.2f}s ({chars_per_sec:,.0f} chars/sec) - Qdrant: {'Success' if qdrant_success else 'Failed'}")
        return qdrant_success, True  # Return True for graph_success since we're not using it
        
    except Exception as e:
        doc_duration = time.time() - doc_start_time
        logger.error(f"❌ Error processing document {file_path} after {doc_duration:.2f}s: {str(e)}")
        logger.debug(f"Document processing traceback: {traceback.format_exc()}")
        return False, False



async def _chunk_and_embed_async(
    doc_ingestion: 'DocumentIngestion',
    text: str,
    doc_id: str,
    user_id: str,
    doc_name: str,
    chunk_size: int,
    chunk_overlap: int
) -> List[PointStruct]:
    """Chunk text and create embeddings asynchronously."""
    start_time = time.time()
    
    try:
        # Run chunking and embedding in an executor
        loop = asyncio.get_event_loop()
        
        # Chunk the text
        chunk_start_time = time.time()
        chunks = await loop.run_in_executor(None, doc_ingestion._chunk_text, text, chunk_size, chunk_overlap)
        chunk_duration = time.time() - chunk_start_time
        
        if not chunks:
            logger.error(f"No chunks created for document {doc_id}")
            return []
        
        logger.debug(f"Chunking completed in {chunk_duration:.2f}s for document {doc_id}")
        
        # Create embedding tasks for all chunks in parallel
        embed_start_time = time.time()
        dense_task = loop.run_in_executor(None, doc_ingestion.get_dense_embeddings_batch, chunks)
        sparse_task = loop.run_in_executor(None, doc_ingestion.get_sparse_embeddings_batch, chunks)
        
        # Wait for both embedding tasks to complete
        dense_embeddings, sparse_embeddings = await asyncio.gather(dense_task, sparse_task)
        embed_duration = time.time() - embed_start_time
        
        if not dense_embeddings or not sparse_embeddings:
            logger.error(f"Failed to get embeddings for document {doc_id}")
            return []
        
        if len(dense_embeddings) != len(sparse_embeddings) or len(dense_embeddings) != len(chunks):
            logger.error(f"Mismatch in embedding lengths for document {doc_id}")
            return []
        
        logger.debug(f"Embedding generation completed in {embed_duration:.2f}s for document {doc_id}")
        
        # Create points
        points_start_time = time.time()
        points = []
        for i, chunk in enumerate(chunks):
            points.append(
                PointStruct(
                    id=str(uuid.uuid4()),
                    vector={
                        "dense": dense_embeddings[i],
                        "sparse": sparse_embeddings[i],
                    },
                    payload={
                        "doc_id": doc_id,
                        "user_id": user_id,
                        "doc_name": doc_name,
                        "text": chunk,
                    },
                )
            )
        
        points_duration = time.time() - points_start_time
        total_duration = time.time() - start_time
        
        logger.info(f"Created {len(points)} points in {total_duration:.2f}s (chunk: {chunk_duration:.2f}s, embed: {embed_duration:.2f}s, points: {points_duration:.2f}s)")
        return points
        
    except Exception as e:
        total_duration = time.time() - start_time
        logger.error(f"Error chunking and embedding document {doc_id} after {total_duration:.2f}s: {str(e)}")
        logger.debug(f"Chunking/embedding traceback: {traceback.format_exc()}")
        return []


# async def _ingest_summary_async(
#     doc_ingestion: 'DocumentIngestion',
#     summary: str,
#     doc_id: str,
#     user_id: str,
#     doc_name: str
# ) -> bool:
#     """Ingest summary to graph asynchronously."""
#     try:
#         # Run the sync graph ingestion in an executor
#         loop = asyncio.get_event_loop()
#         result = await loop.run_in_executor(
#             None, doc_ingestion.ingest_summaries_to_graph, summary, doc_id, user_id, doc_name
#         )
#         return result
#     except Exception as e:
#         logger.error(f"Error ingesting summary for document {doc_id}: {str(e)}")
#         return False


async def _upsert_points_async(
    qdrant_client: QdrantClient,
    collection_name: str,
    points: List[PointStruct],
    doc_id: str,
    batch_size: int = 50,  # Smaller batches to avoid timeouts
    max_retries: int = 3
) -> bool:
    """Upsert points to Qdrant asynchronously with chunking and retry logic."""
    upsert_start_time = time.time()
    
    try:
        if not points:
            logger.error(f"No points to upsert for document {doc_id}")
            return False
        
        logger.info(f"Upserting {len(points)} points for document {doc_id} in batches of {batch_size}")
        
        # Split points into smaller batches to avoid timeouts
        total_batches = (len(points) + batch_size - 1) // batch_size
        successful_batches = 0
        
        for i in range(0, len(points), batch_size):
            batch_start_time = time.time()
            batch_points = points[i:i + batch_size]
            batch_num = i // batch_size + 1
            
            logger.debug(f"Processing batch {batch_num}/{total_batches} ({len(batch_points)} points) for document {doc_id}")
            
            # Retry logic for each batch
            batch_success = False
            for attempt in range(max_retries):
                attempt_start_time = time.time()
                try:
                    # Run the sync upsert in an executor with timeout
                    loop = asyncio.get_event_loop()
                    await asyncio.wait_for(
                        loop.run_in_executor(None, qdrant_client.upsert, collection_name, batch_points),
                        timeout=60  # 1 minute timeout per batch
                    )
                    
                    batch_duration = time.time() - batch_start_time
                    attempt_duration = time.time() - attempt_start_time
                    logger.debug(f"Upserted batch {batch_num}/{total_batches} in {batch_duration:.2f}s (attempt: {attempt_duration:.2f}s)")
                    batch_success = True
                    successful_batches += 1
                    break
                    
                except asyncio.TimeoutError:
                    attempt_duration = time.time() - attempt_start_time
                    logger.warning(f"Timeout on batch {batch_num}/{total_batches} after {attempt_duration:.2f}s (attempt {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    else:
                        logger.error(f"Failed to upsert batch {batch_num}/{total_batches} after {max_retries} timeout attempts")
                        
                except Exception as e:
                    attempt_duration = time.time() - attempt_start_time
                    logger.warning(f"Error on batch {batch_num}/{total_batches} after {attempt_duration:.2f}s: {str(e)} (attempt {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    else:
                        logger.error(f"Failed to upsert batch {batch_num}/{total_batches} after {max_retries} attempts: {str(e)}")
            
            if not batch_success:
                batch_duration = time.time() - batch_start_time
                logger.error(f"Failed to upsert batch {batch_num}/{total_batches} after {batch_duration:.2f}s")
        
        upsert_duration = time.time() - upsert_start_time
        points_per_sec = len(points) / upsert_duration if upsert_duration > 0 else 0
        
        # Check if all batches were successful
        if successful_batches == total_batches:
            logger.info(f"Successfully upserted all {len(points)} points in {upsert_duration:.2f}s ({points_per_sec:.0f} points/sec)")
            return True
        else:
            logger.error(f"Only {successful_batches}/{total_batches} batches successful in {upsert_duration:.2f}s")
            return False
        
    except Exception as e:
        upsert_duration = time.time() - upsert_start_time
        logger.error(f"Error upserting points for document {doc_id} after {upsert_duration:.2f}s: {str(e)}")
        logger.debug(f"Upsert traceback: {traceback.format_exc()}")
        return False


def run_batch_ingestion_pipeline_sync(file_paths: List[str], **kwargs) -> List[Tuple[str, bool, bool]]:
    """
    Synchronous wrapper for the async run_batch_ingestion_pipeline function with caching.
    
    Args:
        file_paths: List of paths to PDF files to process
        **kwargs: Additional arguments passed to run_batch_ingestion_pipeline (including cache_dir)
        
    Returns:
        List of tuples (file_path, qdrant_success, placeholder_boolean) for each document
    """
    return asyncio.run(run_batch_ingestion_pipeline(file_paths, **kwargs))




if __name__ == "__main__":
    # Set up environment variables for Qdrant Cloud and Jina
    import os
    os.environ["QDRANT_API_KEY"] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.fnZYNB72ZW5QcwMp5L-i4qCrw06nX4pbptNK4UjSi0U"
    os.environ["QDRANT_URL"] = "https://3312ccd1-6af5-4b15-8e3e-60ddbdd7b38a.europe-west3-0.gcp.cloud.qdrant.io:6333/"
    # Add your Jina API key here
    # os.environ["JINA_API_KEY"] = "your_jina_api_key_here"
    
    print("🚀 Starting Qdrant Cloud Document Ingestion with Caching & Jina Embeddings")
    print("=" * 70)
    print(f"✓ QDRANT_API_KEY configured")
    print(f"✓ QDRANT_URL: {os.getenv('QDRANT_URL')}")
    print(f"✓ JINA_API_KEY: {'configured' if os.getenv('JINA_API_KEY') else 'NOT SET - Required!'}")
    print(f"✓ Collection: document_chunks")
    print(f"✓ Cache directory: pdf_cache (automatic caching enabled)")
    print(f"✓ Embeddings: Jina v3 (1024D) + FastEmbed sparse")
    print("=" * 70)
    
    # Example usage for batch processing (4 documents)
    file_paths = [
        "mp_materials/pdfs/amendment.pdf",
        "mp_materials/pdfs/contract.pdf", 
        "mp_materials/pdfs/exhibit-3.1.pdf",
        "mp_materials/pdfs/exhibit-10.7.pdf"
    ]
    
    # Filter to only existing files
    existing_files = [f for f in file_paths if os.path.exists(f)]
    
    if not existing_files:
        print("❌ No PDF files found to process")
        exit(1)
    
    # Check if required API keys are set
    if not os.getenv("JINA_API_KEY"):
        print("❌ JINA_API_KEY is required for embeddings generation")
        print("Please set your Jina API key:")
        print("export JINA_API_KEY='your_jina_api_key_here'")
        exit(1)
    
    print(f"📄 Found {len(existing_files)} files to process:")
    for i, file_path in enumerate(existing_files, 1):
        print(f"  {i}. {file_path}")
    print()
    
    try:
        print("⏱️ Starting batch processing...")
        start_time = time.time()
        
        results = run_batch_ingestion_pipeline_sync(
            file_paths=existing_files,
            user_id="test_user",
            collection_name="document_chunks",  # Using your cloud collection
            chunk_size=1280,
            chunk_overlap=256,
            cache_dir="pdf_cache"  # Optional: specify custom cache directory
        )
        print("THIS IS RESULTS", results)
        
        total_duration = time.time() - start_time
        
        print("\n📊 PROCESSING RESULTS:")
        print("-" * 50)
        successful = 0
        for file_path, qdrant_success, _ in results:
            filename = os.path.basename(file_path)
            status = "✅" if qdrant_success else "❌"
            print(f"{status} {filename}")
            if qdrant_success:
                successful += 1
        
        print("-" * 50)
        docs_per_sec = len(existing_files) / total_duration if total_duration > 0 else 0
        print(f"📈 SUMMARY: {successful}/{len(existing_files)} documents processed successfully")
        print(f"⏱️ Total time: {total_duration:.2f}s ({docs_per_sec:.1f} docs/sec)")
        if successful > 0:
            print("🎉 Documents stored in Qdrant Cloud collection 'document_chunks'")
        
    except Exception as e:
        print(f"❌ Error in batch processing: {str(e)}")
        import traceback
        traceback.print_exc()