#!/usr/bin/env python3
"""
Mark a session as completed
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from api.models.database import SessionLocal, UploadSession, UploadStatus, LatticeStatus, to_db_id
from datetime import datetime, timezone


def mark_session_completed(session_id: str):
    """Mark a session as completed"""
    db = SessionLocal()
    
    try:
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if session:
            print(f"Found session {session_id}")
            print(f"Current status: {session.status.value}")
            
            # Mark as completed
            session.status = UploadStatus.COMPLETED
            session.lattice_status = LatticeStatus.COMPLETED
            session.lattice_completed_at = datetime.now(timezone.utc)
            
            db.commit()
            print(f"✅ Marked session as COMPLETED")
        else:
            print(f"Session {session_id} not found")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    session_id = sys.argv[1] if len(sys.argv) > 1 else "188c1893-ebdf-2f5b-b9d4-60208c451927"
    mark_session_completed(session_id) 