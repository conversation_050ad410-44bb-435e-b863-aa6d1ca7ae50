#!/usr/bin/env python3
"""
Script to download required NLTK data
"""
import nltk

def download_nltk_data():
    """Download required NLTK data for the project."""
    print("📥 Downloading NLTK data...")
    
    # List of required NLTK data
    required_data = [
        'punkt',  # For word tokenization
        'punkt_tab',  # For tab-separated tokenization
        'stopwords',  # For stop words (if needed)
        'averaged_perceptron_tagger',  # For POS tagging (if needed)
        'maxent_ne_chunker',  # For named entity recognition (if needed)
        'words',  # For word lists
        'treebank'  # For training data
    ]
    
    for data_name in required_data:
        try:
            print(f"Downloading {data_name}...")
            nltk.download(data_name, quiet=True)
            print(f"✅ {data_name} downloaded successfully")
        except Exception as e:
            print(f"❌ Error downloading {data_name}: {e}")
    
    # Also try to download specific punkt resources
    try:
        print("Downloading punkt English tokenizer...")
        nltk.download('punkt', quiet=True)
        print("✅ punkt English tokenizer downloaded successfully")
    except Exception as e:
        print(f"❌ Error downloading punkt English: {e}")
    
    print("\n🎉 NLTK data download completed!")

if __name__ == "__main__":
    download_nltk_data() 