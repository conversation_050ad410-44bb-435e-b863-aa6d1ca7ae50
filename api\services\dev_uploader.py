import os
import asyncio
import hashlib
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import json
import boto3

from sqlalchemy.orm import Session
from api.models.database import UploadSession, UploadedFile, FileStatus, UploadStatus, to_db_id, from_db_id
from api.services.storage import storage_service
from api.services.dev_cache import dev_session_cache
from api.utils.logger import logger


class DevUploaderService:
    """Service for uploading local files in development mode"""
    
    FILE_MAPPINGS = {
        "mp_materials": {
            "base_path": "./mp_materials",
            "company_name": "MP Materials",
            "session_type": "investor_relations"
        },
        "resido": {
            "base_path": "./resido",
            "company_name": "Resido",
            "session_type": "mixed"
        }
    }
    
    ALLOWED_EXTENSIONS = {'.pdf', '.xls', '.xlsx'}
    BATCH_SIZE = 5  # Upload 5 files concurrently
    
    def __init__(self):
        self.upload_stats = {}
    
    def scan_directory(
        self, 
        source: str, 
        file_types: Optional[List[str]] = None,
        subdirectory: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """Scan directory for files matching criteria"""
        
        if source not in self.FILE_MAPPINGS:
            raise ValueError(f"Invalid source: {source}")
        
        base_path = Path(self.FILE_MAPPINGS[source]["base_path"])
        if subdirectory:
            base_path = base_path / subdirectory
        
        if not base_path.exists():
            logger.error(f"Directory not found: {base_path}")
            return []
        
        # Determine extensions to look for
        if file_types:
            extensions = {f'.{ft.lower()}' for ft in file_types}
        else:
            extensions = self.ALLOWED_EXTENSIONS
        
        files = []
        for ext in extensions:
            for file_path in base_path.rglob(f'*{ext}'):
                if file_path.is_file():
                    rel_path = file_path.relative_to(Path(self.FILE_MAPPINGS[source]["base_path"]))
                    files.append({
                        'full_path': str(file_path),
                        'relative_path': str(rel_path),
                        'filename': file_path.name,
                        'size': file_path.stat().st_size,
                        'extension': file_path.suffix.lower(),
                        'directory': str(rel_path.parent),
                        'source': source
                    })
        
        logger.info(f"Found {len(files)} files in {source}")
        return sorted(files, key=lambda x: x['relative_path'])
    
    def calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of a file"""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        
        return sha256_hash.hexdigest()
    
    async def upload_file_to_s3(
        self, 
        file_info: Dict[str, str], 
        session_id: str
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """Upload a single file to S3"""
        try:
            # Generate S3 key
            safe_filename = "".join(c for c in file_info['filename'] if c.isalnum() or c in ".-_")
            s3_key = f"uploads/{from_db_id(session_id)}/{file_info['directory']}/{safe_filename}"
            s3_key = s3_key.replace('//', '/')  # Clean up double slashes
            
            # Read file
            with open(file_info['full_path'], 'rb') as f:
                file_content = f.read()
            
            # Upload to S3
            storage_service.client.put_object(
                Bucket=storage_service.bucket_name,
                Key=s3_key,
                Body=file_content,
                ContentType=self._get_content_type(file_info['extension'])
            )
            
            # Generate bucket URL
            bucket_url = f"{storage_service.client._endpoint.host}/{storage_service.bucket_name}/{s3_key}"
            
            logger.info(f"Uploaded {file_info['filename']} to {s3_key}")
            return True, s3_key, bucket_url
            
        except Exception as e:
            logger.error(f"Failed to upload {file_info['filename']}: {str(e)}")
            return False, None, None
    
    def create_upload_session(
        self,
        db: Session,
        all_files: List[Dict[str, str]],
        source: str,
        file_types: Optional[List[str]] = None,
        subdirectory: Optional[str] = None,
        auto_process: bool = True,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> UploadSession:
        """Create upload session and file records"""
        # Create session with optional deterministic ID
        session_data = {
            "total_files": len(all_files),
            "user_id": user_id or "dev_upload",
            "company_name": "Mixed" if source == "both" else self.FILE_MAPPINGS.get(source, {}).get("company_name", "Unknown"),
            "session_type": "dev_upload",
            "session_metadata": {
                "source": source,
                "file_types": file_types,
                "subdirectory": subdirectory,
                "auto_process": auto_process,
                "upload_started_at": datetime.utcnow().isoformat(),
                "upload_status": "pending",
                "is_dev_session": True
            }
        }
        
        if session_id:
            session_data["id"] = to_db_id(session_id)
            
        session = UploadSession(**session_data)
        db.add(session)
        db.commit()
        
        logger.info(f"Created dev upload session {from_db_id(session.id)} for {len(all_files)} files")
        
        # Create file records
        for file_info in all_files:
            file_hash = self.calculate_file_hash(file_info['full_path'])
            
            file_record = UploadedFile(
                session_id=to_db_id(session.id),
                filename=file_info['filename'],
                file_size=file_info['size'],
                content_type=self._get_content_type(file_info['extension']),
                file_hash=file_hash,
                status=FileStatus.PENDING,
                file_metadata={
                    "source": file_info['source'],
                    "directory": file_info['directory'],
                    "relative_path": file_info['relative_path']
                }
            )
            db.add(file_record)
        
        db.commit()
        return session
    
    async def upload_files_background(
        self,
        session_id: str,
        all_files: List[Dict[str, str]],
        auto_process: bool = True
    ) -> None:
        """Upload files in background"""
        from api.models.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Get session
            session = db.query(UploadSession).filter(
                UploadSession.id == to_db_id(session_id)
            ).first()
            
            if not session:
                logger.error(f"Session {session_id} not found for background upload")
                return
            
            # Update status
            if isinstance(session.session_metadata, str):
                metadata = json.loads(session.session_metadata)
            else:
                metadata = session.session_metadata or {}
            metadata["upload_status"] = "in_progress"
            session.session_metadata = metadata
            db.commit()
            
            # Process files in batches
            uploaded_count = 0
            failed_count = 0
            total_size = 0
            
            logger.info(f"Starting upload of {len(all_files)} files in batches of {self.BATCH_SIZE}")
            
            for i in range(0, len(all_files), self.BATCH_SIZE):
                batch = all_files[i:i + self.BATCH_SIZE]
                
                # Get file records for this batch
                batch_filenames = [f['filename'] for f in batch]
                file_records = db.query(UploadedFile).filter(
                    UploadedFile.session_id == to_db_id(session_id),
                    UploadedFile.filename.in_(batch_filenames)
                ).all()
                
                # Create mapping
                file_record_map = {fr.filename: fr for fr in file_records}
                
                # Upload batch
                for file_info in batch:
                    file_record = file_record_map.get(file_info['filename'])
                    if not file_record:
                        logger.error(f"File record not found for {file_info['filename']}")
                        continue
                    
                    success, s3_key, bucket_url = await self.upload_file_to_s3(file_info, session_id)
                    
                    if success:
                        file_record.bucket_key = s3_key
                        file_record.bucket_url = bucket_url
                        file_record.status = FileStatus.UPLOADED
                        uploaded_count += 1
                        total_size += file_record.file_size
                    else:
                        file_record.status = FileStatus.FAILED
                        file_record.error_message = "Upload failed"
                        failed_count += 1
                
                db.commit()
                
                # Update progress
                progress = ((i + len(batch)) / len(all_files)) * 100
                logger.info(f"Upload progress: {progress:.1f}% ({uploaded_count} succeeded, {failed_count} failed)")
            
            # Update session
            session.total_size_bytes = total_size
            if isinstance(session.session_metadata, str):
                metadata = json.loads(session.session_metadata)
            else:
                metadata = session.session_metadata or {}
            metadata.update({
                "upload_completed_at": datetime.utcnow().isoformat(),
                "uploaded_files": uploaded_count,
                "failed_files": failed_count,
                "upload_status": "completed"
            })
            session.session_metadata = metadata
            db.commit()
            
            logger.info(f"Background upload completed for session {session_id}: {uploaded_count} uploaded, {failed_count} failed")
            
            # Trigger processing if requested
            if auto_process and uploaded_count > 0:
                from api.services.processor import file_processor
                await file_processor.process_session(session_id)
                
        except Exception as e:
            logger.error(f"Error in background upload: {str(e)}", exc_info=True)
            # Update session status
            if 'session' in locals() and session:
                if isinstance(session.session_metadata, str):
                    metadata = json.loads(session.session_metadata)
                else:
                    metadata = session.session_metadata or {}
                metadata["upload_status"] = "failed"
                metadata["error"] = str(e)
                session.session_metadata = metadata
                db.commit()
        finally:
            db.close()
    
    async def process_local_files(
        self,
        db: Session,
        source: str = "both",
        file_types: Optional[List[str]] = None,
        subdirectory: Optional[str] = None,
        auto_process: bool = True,
        user_id: Optional[str] = None,
        background_tasks = None
    ) -> Dict[str, Any]:
        """Process files from local directories"""
        
        logger.info(f"Processing local files - source: {source}, subdirectory: {subdirectory}, auto_process: {auto_process}")
        
        # Check cache first (only for single source, not "both")
        if source != "both":
            cached_session_id = dev_session_cache.get_session(source, subdirectory, db, user_id=user_id)
            if cached_session_id:
                # Check if session is completed
                from api.models.database import UploadStatus
                cached_session = db.query(UploadSession).filter(
                    UploadSession.id == to_db_id(cached_session_id)
                ).first()
                
                if cached_session and cached_session.status == UploadStatus.COMPLETED:
                    logger.info(f"Found completed session {cached_session_id}, creating new session with same files")
                    # Create new session but copy the files from the completed session
                    return await self._create_session_from_existing(cached_session, db, user_id, auto_process, background_tasks)
                else:
                    logger.info(f"Using cached session {cached_session_id} for {source}")
                    return {
                        "status": "success",
                        "session_id": cached_session_id,
                        "message": "Using existing uploaded files from cache",
                        "cached": True,
                        "auto_process": auto_process
                    }
        
        # Determine which directories to scan
        sources_to_scan = []
        if source == "both":
            sources_to_scan = ["mp_materials", "resido"]
        elif source in self.FILE_MAPPINGS:
            sources_to_scan = [source]
        else:
            raise ValueError(f"Invalid source: {source}")
        
        # Scan directories
        all_files = []
        for src in sources_to_scan:
            files = self.scan_directory(src, file_types, subdirectory)
            all_files.extend(files)
        
        if not all_files:
            logger.warning(f"No files found for source: {source}, subdirectory: {subdirectory}")
            return {
                "status": "error",
                "message": "No files found matching criteria"
            }
        
        logger.info(f"Found {len(all_files)} files to upload")
        
        # For single source, use deterministic session ID
        if source != "both":
            session_id = dev_session_cache._generate_session_id(source, subdirectory)
            # Check if session already exists
            existing_session = db.query(UploadSession).filter(
                UploadSession.id == to_db_id(session_id)
            ).first()
            
            if existing_session:
                logger.info(f"Existing session status: {existing_session.status.value}")
                # Check if session is completed
                from api.models.database import UploadStatus
                if existing_session.status == UploadStatus.COMPLETED:
                    logger.info(f"Found completed session {session_id}, creating new session with same files")
                    # Create new session but copy the files from the completed session
                    return await self._create_session_from_existing(existing_session, db, user_id, auto_process, background_tasks)
                else:
                    logger.info(f"Found existing session {session_id} in database")
                    return {
                        "status": "success",
                        "session_id": session_id,
                        "total_files": existing_session.total_files,
                        "message": "Session already exists",
                        "cached": True,
                        "auto_process": auto_process
                    }
        else:
            session_id = None
        
        # Create session
        session = self.create_upload_session(
            db=db,
            all_files=all_files,
            source=source,
            file_types=file_types,
            subdirectory=subdirectory,
            auto_process=auto_process,
            user_id=user_id,
            session_id=session_id
        )
        
        session_id = from_db_id(session.id)
        
        # Save to cache if single source
        if source != "both":
            dev_session_cache.save_session(source, subdirectory, session_id, len(all_files))
            logger.info(f"Saved session {session_id} to cache")
        
        # Start background upload
        if background_tasks:
            background_tasks.add_task(
                self.upload_files_background,
                session_id=session_id,
                all_files=all_files,
                auto_process=auto_process
            )
        else:
            # Fallback for when called without BackgroundTasks
            asyncio.create_task(
                self.upload_files_background(
                    session_id=session_id,
                    all_files=all_files,
                    auto_process=auto_process
                )
            )
        
        return {
            "status": "success",
            "session_id": session_id,
            "total_files": len(all_files),
            "message": "Upload started in background. Poll /upload/status/{session_id} for progress",
            "cached": False,
            "auto_process": auto_process
        }
    
    async def process_specific_files(
        self,
        db: Session,
        file_paths: List[str],
        user_id: Optional[str] = None,
        auto_process: bool = True
    ) -> Dict[str, Any]:
        """Process specific files by their paths"""
        
        # Validate and gather file info
        valid_files = []
        for file_path in file_paths:
            path = Path(file_path)
            if not path.exists():
                logger.warning(f"File not found: {file_path}")
                continue
            
            if path.suffix.lower() not in self.ALLOWED_EXTENSIONS:
                logger.warning(f"Invalid file type: {file_path}")
                continue
            
            # Determine source
            source = None
            for src, mapping in self.FILE_MAPPINGS.items():
                if file_path.startswith(mapping["base_path"]):
                    source = src
                    break
            
            if not source:
                logger.warning(f"File not in recognized directory: {file_path}")
                continue
            
            rel_path = path.relative_to(Path(self.FILE_MAPPINGS[source]["base_path"]))
            
            valid_files.append({
                'full_path': str(path),
                'relative_path': str(rel_path),
                'filename': path.name,
                'size': path.stat().st_size,
                'extension': path.suffix.lower(),
                'directory': str(rel_path.parent),
                'source': source
            })
        
        if not valid_files:
            return {
                "status": "error",
                "message": "No valid files found"
            }
        
        # Use the main processing function with the specific files
        self.all_files = valid_files  # Temporary override
        result = await self.process_local_files(
            db=db,
            source="custom",
            user_id=user_id,
            auto_process=auto_process
        )
        
        return result
    
    async def _create_session_from_existing(
        self, 
        existing_session: UploadSession,
        db: Session,
        user_id: Optional[str],
        auto_process: bool,
        background_tasks: Optional[Any] = None
    ) -> Dict[str, Any]:
        """Create a new session by copying files from an existing session"""
        logger.info(f"Creating new session from existing session {existing_session.id}")
        
        # Create new session
        new_session = UploadSession(
            user_id=user_id or existing_session.user_id,
            company_name=existing_session.company_name,
            session_type=existing_session.session_type,
            total_files=existing_session.total_files,
            total_size_bytes=existing_session.total_size_bytes,
            status=UploadStatus.PENDING
        )
        db.add(new_session)
        db.commit()
        
        logger.info(f"Created new session {from_db_id(new_session.id)}")
        
        # Copy all files from existing session
        existing_files = db.query(UploadedFile).filter(
            UploadedFile.session_id == existing_session.id
        ).all()
        
        copied_count = 0
        for existing_file in existing_files:
            new_file = UploadedFile(
                session_id=new_session.id,
                filename=existing_file.filename,
                file_size=existing_file.file_size,
                content_type=existing_file.content_type,
                bucket_key=existing_file.bucket_key,  # Reuse same S3 location
                bucket_url=existing_file.bucket_url,
                file_hash=existing_file.file_hash,
                status=FileStatus.UPLOADED,
                file_metadata=existing_file.file_metadata
            )
            db.add(new_file)
            copied_count += 1
        
        # Update session status
        new_session.status = UploadStatus.PENDING  # Ready for processing
        new_session.processed_files = copied_count
        db.commit()
        
        logger.info(f"Copied {copied_count} files to new session {from_db_id(new_session.id)}")
        
        # Auto-process if requested
        if auto_process and background_tasks:
            from api.routers.process import process_with_lattice
            background_tasks.add_task(
                process_with_lattice,
                from_db_id(new_session.id),
                None,  # No headers
                None   # No category headers
            )
            logger.info(f"Started auto-processing for new session {from_db_id(new_session.id)}")
        
        return {
            "status": "success",
            "session_id": from_db_id(new_session.id),
            "message": f"Created new session with {copied_count} files from completed session",
            "cached": False,
            "auto_process": auto_process,
            "original_session_id": from_db_id(existing_session.id)
        }
    
    def _get_content_type(self, extension: str) -> str:
        """Get content type for file extension"""
        content_types = {
            '.pdf': 'application/pdf',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
        return content_types.get(extension.lower(), 'application/octet-stream')


# Singleton instance
dev_uploader = DevUploaderService()