#!/usr/bin/env python3
"""
Reset all dev sessions in the database
This is useful when you want to force new uploads in dev mode
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from api.models.database import SessionLocal, UploadSession, UploadedFile, <PERSON>tticeResult, LatticeHeader, to_db_id
from api.services.dev_cache import dev_session_cache
import uuid
from sqlalchemy import text


def reset_dev_sessions(delete_files=False):
    """Delete or reset dev sessions from database and clear cache"""
    db = SessionLocal()
    
    try:
        # Get the deterministic session IDs that dev mode uses
        sources = ["mp_materials", "resido", "both"]
        subdirectories = [None, "pdfs", "Investor Relations", "Financial Database"]
        
        processed_count = 0
        
        # Generate all possible dev session IDs
        for source in sources:
            for subdir in subdirectories:
                session_id = dev_session_cache._generate_session_id(source, subdir)
                
                # Find session
                session = db.query(UploadSession).filter(
                    UploadSession.id == to_db_id(session_id)
                ).first()
                
                if session:
                    print(f"Found session {session_id} for {source}:{subdir or 'all'}")
                    
                    # Count related data
                    file_count = db.query(UploadedFile).filter(
                        UploadedFile.session_id == to_db_id(session_id)
                    ).count()
                    
                    result_count = db.query(LatticeResult).filter(
                        LatticeResult.session_id == to_db_id(session_id)
                    ).count()
                    
                    header_count = db.query(LatticeHeader).filter(
                        LatticeHeader.session_id == to_db_id(session_id)
                    ).count()
                    
                    print(f"  - {file_count} files, {result_count} lattice results, {header_count} headers")
                    
                    if delete_files or True:  # Always delete for now since rename doesn't work with FKs
                        # Delete in order to respect foreign key constraints
                        print(f"  - Deleting lattice results...")
                        deleted = db.query(LatticeResult).filter(
                            LatticeResult.session_id == to_db_id(session_id)
                        ).delete(synchronize_session=False)
                        print(f"    Deleted {deleted} results")
                        
                        print(f"  - Deleting lattice headers...")
                        deleted = db.query(LatticeHeader).filter(
                            LatticeHeader.session_id == to_db_id(session_id)
                        ).delete(synchronize_session=False)
                        print(f"    Deleted {deleted} headers")
                        
                        print(f"  - Deleting files...")
                        deleted = db.query(UploadedFile).filter(
                            UploadedFile.session_id == to_db_id(session_id)
                        ).delete(synchronize_session=False)
                        print(f"    Deleted {deleted} files")
                        
                        print(f"  - Deleting session...")
                        db.delete(session)
                        
                        # Commit after each major operation to avoid long locks
                        db.commit()
                        print(f"    ✓ Session deleted successfully")
                    
                    processed_count += 1
        
        # Clear the cache file
        dev_session_cache.clear_cache()
        
        print(f"\n✅ Processed {processed_count} dev sessions")
        print("✅ Cleared dev cache")
        print("\nYou can now create fresh sessions with /api/dev/process")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        db.close()


def quick_reset():
    """Just clear the session without deleting files - mark as completed"""
    db = SessionLocal()
    
    try:
        # Get the specific session that's stuck
        session_id = "188c1893-ebdf-2f5b-b9d4-60208c451927"
        
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if session:
            print(f"Found session {session_id}")
            # Just mark it as completed so it won't be reused
            from api.models.database import UploadStatus
            session.status = UploadStatus.COMPLETED
            session.company_name = f"OLD_DEV_{session.company_name or 'Unknown'}"
            db.commit()
            print("✅ Marked session as completed")
        
        # Clear cache
        dev_session_cache.clear_cache()
        print("✅ Cleared cache")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Reset dev sessions')
    parser.add_argument('--delete-files', action='store_true', 
                        help='Actually delete files. Default is to mark as completed.')
    parser.add_argument('--quick', action='store_true',
                        help='Just mark session as completed without deleting')
    args = parser.parse_args()
    
    if args.quick:
        print("🔧 Quick reset - marking session as completed...")
        quick_reset()
    else:
        print("🔧 Resetting all dev sessions...")
        print("⚠️  This will delete all related data. This may take a moment...")
        reset_dev_sessions(delete_files=True) 