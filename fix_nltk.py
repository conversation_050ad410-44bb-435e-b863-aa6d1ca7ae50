#!/usr/bin/env python3
"""
Comprehensive NLTK data fix script
"""
import nltk
import os
import sys

def fix_nltk_data():
    """Download all necessary NLTK data."""
    print("🔧 Fixing NLTK data issues...")
    
    # Download all punkt-related data
    punkt_data = [
        'punkt',
        'punkt_tab',
        'tokenizers/punkt',
        'tokenizers/punkt_tab',
        'tokenizers/punkt/english'
    ]
    
    for data_name in punkt_data:
        try:
            print(f"Downloading {data_name}...")
            nltk.download(data_name, quiet=True)
            print(f"✅ {data_name} downloaded successfully")
        except Exception as e:
            print(f"⚠️ Could not download {data_name}: {e}")
    
    # Download other useful NLTK data
    other_data = [
        'stopwords',
        'averaged_perceptron_tagger',
        'maxent_ne_chunker',
        'words',
        'treebank'
    ]
    
    for data_name in other_data:
        try:
            print(f"Downloading {data_name}...")
            nltk.download(data_name, quiet=True)
            print(f"✅ {data_name} downloaded successfully")
        except Exception as e:
            print(f"⚠️ Could not download {data_name}: {e}")
    
    # Test if punkt works
    try:
        from nltk.tokenize import word_tokenize
        test_result = word_tokenize("This is a test sentence.")
        print(f"✅ NLTK tokenization test passed: {test_result}")
    except Exception as e:
        print(f"❌ NLTK tokenization test failed: {e}")
        print("⚠️ Will use fallback tokenization in the main code")
    
    print("\n🎉 NLTK data fix completed!")

if __name__ == "__main__":
    fix_nltk_data() 