import json
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import uuid

from sqlalchemy.orm import Session
from api.models.database import UploadSession, UploadedFile, to_db_id, from_db_id, FileStatus
from api.services.storage import storage_service
from api.utils.logger import logger


class DevSessionCache:
    """Manages persistent cache for development file uploads"""
    
    def __init__(self):
        self.cache_dir = Path(".cache")
        self.cache_file = self.cache_dir / "dev_sessions.json"
        self._ensure_cache_dir()
        logger.info(f"DevSessionCache initialized with cache file: {self.cache_file}")
        
    def _ensure_cache_dir(self):
        """Ensure cache directory exists"""
        self.cache_dir.mkdir(exist_ok=True)
        if not self.cache_file.exists():
            self._save_cache({})
            logger.info("Created new cache file")
    
    def _generate_session_id(self, source: str, subdirectory: Optional[str] = None) -> str:
        """Generate deterministic session ID for a source"""
        key = f"dev:{source}:{subdirectory or 'all'}"
        # Generate deterministic UUID from hash
        hash_digest = hashlib.sha256(key.encode()).digest()
        # Use first 16 bytes for UUID
        deterministic_uuid = str(uuid.UUID(bytes=hash_digest[:16]))
        logger.debug(f"Generated session ID {deterministic_uuid} for key: {key}")
        return deterministic_uuid
    
    def _load_cache(self) -> Dict[str, Any]:
        """Load cache from JSON file"""
        try:
            with open(self.cache_file, 'r') as f:
                cache = json.load(f)
                logger.debug(f"Loaded cache with {len(cache)} entries")
                return cache
        except Exception as e:
            logger.error(f"Error loading cache: {str(e)}")
            return {}
    
    def _save_cache(self, cache: Dict[str, Any]) -> None:
        """Save cache to JSON file"""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(cache, f, indent=2)
                logger.debug(f"Saved cache with {len(cache)} entries")
        except Exception as e:
            logger.error(f"Error saving cache: {str(e)}")
    
    def get_session(self, source: str, subdirectory: Optional[str], db: Session, user_id: Optional[str] = None, allow_completed: bool = False) -> Optional[str]:
        """Get cached session ID if it exists and is valid"""
        session_id = self._generate_session_id(source, subdirectory)
        cache_key = f"{source}:{subdirectory or 'all'}"
        
        logger.info(f"Checking cache for {cache_key} with session ID {session_id}")
        
        # Check if session exists in database
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if session:
            logger.info(f"Found existing session {session_id} in database with status: {session.status.value}")
            
            # If session is completed and we don't allow completed sessions, return None
            # This will force creation of a new session with the same files
            from api.models.database import UploadStatus
            if session.status == UploadStatus.COMPLETED and not allow_completed:
                logger.info(f"Session {session_id} is completed, will create new session for reprocessing")
                return None
            
            # Update cache with current info
            cache = self._load_cache()
            cache[cache_key] = {
                "session_id": session_id,
                "created_at": session.created_at.isoformat() if session.created_at else None,
                "last_accessed": datetime.utcnow().isoformat(),
                "file_count": session.total_files,
                "status": session.status.value
            }
            self._save_cache(cache)
            return session_id
        
        # Check cache for orphaned session
        cache = self._load_cache()
        if cache_key in cache:
            cached_info = cache[cache_key]
            logger.info(f"Found cached session info for {cache_key}: {cached_info}")
            
            # Verify S3 files still exist
            if self._verify_s3_session(cached_info.get("session_id"), db):
                logger.info(f"Verified S3 files exist for cached session {cached_info['session_id']}")
                # Update last accessed
                cache[cache_key]["last_accessed"] = datetime.utcnow().isoformat()
                self._save_cache(cache)
                return cached_info["session_id"]
            else:
                logger.warning(f"S3 files not found for cached session {cached_info['session_id']}, removing from cache")
                del cache[cache_key]
                self._save_cache(cache)
        
        logger.info(f"No valid cached session found for {cache_key}")
        return None
    
    def save_session(self, source: str, subdirectory: Optional[str], session_id: str, file_count: int) -> None:
        """Save session info to cache"""
        cache_key = f"{source}:{subdirectory or 'all'}"
        cache = self._load_cache()
        
        cache[cache_key] = {
            "session_id": session_id,
            "created_at": datetime.utcnow().isoformat(),
            "last_accessed": datetime.utcnow().isoformat(),
            "file_count": file_count,
            "source": source,
            "subdirectory": subdirectory,
            "status": "active"
        }
        
        self._save_cache(cache)
        logger.info(f"Saved session {session_id} to cache for {cache_key} with {file_count} files")
    
    def _verify_s3_session(self, session_id: str, db: Session) -> bool:
        """Verify that S3 files for a session still exist"""
        try:
            # Check if at least one file exists in S3
            prefix = f"uploads/{session_id}/"
            objects = storage_service.client.list_objects_v2(
                Bucket=storage_service.bucket_name,
                Prefix=prefix,
                MaxKeys=1
            )
            
            has_objects = objects.get('KeyCount', 0) > 0
            logger.debug(f"S3 verification for session {session_id}: {'found files' if has_objects else 'no files'}")
            return has_objects
            
        except Exception as e:
            logger.error(f"Error verifying S3 session {session_id}: {str(e)}")
            return False
    
    def clear_cache(self) -> Dict[str, Any]:
        """Clear all cache entries"""
        cache = self._load_cache()
        old_count = len(cache)
        self._save_cache({})
        logger.info(f"Cleared cache, removed {old_count} entries")
        return {
            "cleared": old_count,
            "message": f"Cleared {old_count} cached sessions"
        }
    
    def get_cache_status(self) -> Dict[str, Any]:
        """Get current cache status and contents"""
        cache = self._load_cache()
        
        status = {
            "cache_file": str(self.cache_file),
            "total_sessions": len(cache),
            "sessions": {}
        }
        
        for key, info in cache.items():
            status["sessions"][key] = {
                "session_id": info.get("session_id"),
                "created_at": info.get("created_at"),
                "last_accessed": info.get("last_accessed"),
                "file_count": info.get("file_count"),
                "status": info.get("status", "unknown")
            }
        
        logger.info(f"Cache status: {len(cache)} sessions")
        return status
    
    def verify_all_sessions(self, db: Session) -> Dict[str, Any]:
        """Verify all cached sessions and clean up invalid ones"""
        cache = self._load_cache()
        verified = 0
        removed = 0
        
        for key in list(cache.keys()):
            session_info = cache[key]
            session_id = session_info.get("session_id")
            
            if self._verify_s3_session(session_id, db):
                verified += 1
                cache[key]["last_verified"] = datetime.utcnow().isoformat()
                logger.info(f"Verified session {session_id} for {key}")
            else:
                del cache[key]
                removed += 1
                logger.warning(f"Removed invalid session {session_id} for {key}")
        
        self._save_cache(cache)
        
        result = {
            "verified": verified,
            "removed": removed,
            "message": f"Verified {verified} sessions, removed {removed} invalid sessions"
        }
        logger.info(f"Cache verification complete: {result}")
        return result


# Singleton instance
dev_session_cache = DevSessionCache()