#!/usr/bin/env python3
"""
Check the status of a specific session
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from api.models.database import SessionLocal, UploadSession, to_db_id, from_db_id
from api.utils.logger import logger


def check_session_status(session_id: str):
    """Check the status of a specific session"""
    db = SessionLocal()
    
    try:
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if session:
            print(f"Session ID: {session_id}")
            print(f"Status: {session.status.value}")
            print(f"Company: {session.company_name}")
            print(f"Total Files: {session.total_files}")
            print(f"Created: {session.created_at}")
            print(f"Lattice Status: {session.lattice_status.value if session.lattice_status else 'None'}")
            print(f"Lattice Completed: {session.lattice_completed_at if session.lattice_completed_at else 'Not completed'}")
        else:
            print(f"Session {session_id} not found")
            
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    session_id = sys.argv[1] if len(sys.argv) > 1 else "188c1893-ebdf-2f5b-b9d4-60208c451927"
    check_session_status(session_id) 