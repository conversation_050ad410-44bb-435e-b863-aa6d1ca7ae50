from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
import json
import asyncio
from pydantic import BaseModel, validator

from api.models.database import (
    get_db, UploadSession, UploadedFile, DueDiligenceCategory, CategorizationStatus, 
    LatticeStatus, LatticeHeader, LatticeResult, to_db_id, from_db_id
)
from api.services.parallel_categorization import parallel_categorization_service
from api.services.lattice_analysis_service import lattice_analysis_service, get_lattice_matrix
from api.services.new_lattice_service import new_lattice_service
from api.utils.logger import logger


# Create router
router = APIRouter(prefix="/process", tags=["process"])


class LatticeHeadersInput(BaseModel):
    """Input model for lattice headers with flexible options"""
    headers: Optional[List[str]] = None  # General headers applied to all documents
    headers_by_category: Optional[Dict[str, List[str]]] = None  # Category-specific headers
    
    @validator('headers', 'headers_by_category')
    def at_least_one_required(cls, v, values):
        headers = values.get('headers') if 'headers' in values else v
        headers_by_category = values.get('headers_by_category') if 'headers_by_category' in values else v
        
        if not headers and not headers_by_category:
            raise ValueError("Either 'headers' or 'headers_by_category' must be provided")
        return v
    
    @validator('headers', pre=True)
    def validate_general_headers(cls, v):
        if v is None:
            return None
        
        if not isinstance(v, list):
            raise ValueError("headers must be a list")
        
        for header in v:
            if not isinstance(header, str):
                raise ValueError("All headers must be strings")
            if len(header.strip()) == 0:
                raise ValueError("Headers cannot be empty")
            if len(header) > 200:
                raise ValueError(f"Header too long (max 200 chars): {header[:50]}...")
        
        return [h.strip() for h in v if h.strip()]
    
    @validator('headers_by_category', pre=True)
    def validate_category_headers(cls, v):
        if v is None:
            return None
        
        if not isinstance(v, dict):
            raise ValueError("headers_by_category must be a dictionary")
        
        valid_categories = [cat.value for cat in DueDiligenceCategory]
        
        for category, headers in v.items():
            if category not in valid_categories:
                raise ValueError(f"Invalid category '{category}'. Must be one of: {valid_categories}")
            
            if not isinstance(headers, list):
                raise ValueError(f"Headers for category '{category}' must be a list")
            
            for header in headers:
                if not isinstance(header, str):
                    raise ValueError(f"All headers in category '{category}' must be strings")
                if len(header.strip()) == 0:
                    raise ValueError(f"Headers in category '{category}' cannot be empty")
                if len(header) > 200:
                    raise ValueError(f"Header too long in category '{category}' (max 200 chars): {header[:50]}...")
        
        # Clean up empty lists and strip whitespace
        cleaned = {}
        for category, headers in v.items():
            clean_headers = [h.strip() for h in headers if h.strip()]
            if clean_headers:
                cleaned[category] = clean_headers
        
        return cleaned if cleaned else None


@router.post("/{session_id}")
async def start_processing(
    session_id: str,
    background_tasks: BackgroundTasks,
    lattice_headers: Optional[LatticeHeadersInput] = None,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Start processing for a session with optional lattice headers.
    
    If lattice headers are provided, will run lattice analysis after categorization.
    If no headers provided, will only run categorization.
    
    Args:
        session_id: The upload session ID
        lattice_headers: Optional lattice headers configuration
        background_tasks: FastAPI background tasks
        db: Database session
    
    Returns:
        Processing status and poll endpoint information
    """
    logger.info(f"🌟 API: Processing request for session {session_id}")
    
    # Log request details
    if lattice_headers:
        if lattice_headers.headers:
            logger.info(f"📋 Request includes {len(lattice_headers.headers)} general headers")
            logger.debug(f"General headers: {lattice_headers.headers}")
        
        if lattice_headers.headers_by_category:
            total_cat_headers = sum(len(h) for h in lattice_headers.headers_by_category.values())
            logger.info(f"📂 Request includes category headers: {len(lattice_headers.headers_by_category)} categories, {total_cat_headers} total")
            for cat, headers in lattice_headers.headers_by_category.items():
                logger.debug(f"Category {cat}: {len(headers)} headers")
    else:
        logger.info("📋 No lattice headers provided - categorization only")
    
    try:
        # Validate session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            logger.error(f"❌ Session {session_id} not found")
            raise HTTPException(status_code=404, detail="Session not found")
        
        logger.info(f"📊 Session found: {session.company_name or 'Unknown'}, {session.total_files} files")
        
        # Check categorization status
        categorized_files = db.query(UploadedFile).filter(
            UploadedFile.session_id == to_db_id(session_id),
            UploadedFile.categorization_status == CategorizationStatus.COMPLETED
        ).count()
        
        pending_categorization = db.query(UploadedFile).filter(
            UploadedFile.session_id == to_db_id(session_id),
            UploadedFile.categorization_status == CategorizationStatus.PENDING
        ).count()
        
        logger.info(f"📈 Categorization status: {categorized_files} completed, {pending_categorization} pending")
        
        # Store lattice headers if provided
        if lattice_headers:
            logger.info("💾 Storing lattice headers in database")
            
            # Clear old headers
            deleted_count = db.query(LatticeHeader).filter(
                LatticeHeader.session_id == to_db_id(session_id)
            ).delete()
            
            if deleted_count > 0:
                logger.info(f"🗑️  Cleared {deleted_count} old lattice headers")
            
            # Store new headers
            headers_stored = 0
            
            if lattice_headers.headers:
                lattice_header = LatticeHeader(
                    session_id=to_db_id(session_id),
                    category=None,  # General headers
                    headers=lattice_headers.headers,
                    apply_to_all=True
                )
                db.add(lattice_header)
                headers_stored += len(lattice_headers.headers)
                logger.debug(f"Stored {len(lattice_headers.headers)} general headers")
            
            if lattice_headers.headers_by_category:
                for category, headers in lattice_headers.headers_by_category.items():
                    if headers:
                        lattice_header = LatticeHeader(
                            session_id=to_db_id(session_id),
                            category=category,
                            headers=headers,
                            apply_to_all=False
                        )
                        db.add(lattice_header)
                        headers_stored += len(headers)
                        logger.debug(f"Stored {len(headers)} headers for category {category}")
            
            db.commit()
            logger.info(f"✅ Successfully stored {headers_stored} total lattice headers")
        
        # Check for stuck categorization process
        if session.categorization_started_at and not session.categorization_completed_at:
            time_since_start = (datetime.now(timezone.utc) - session.categorization_started_at).total_seconds()
            if time_since_start > 600:  # 10 minutes
                logger.warning(f"⚠️  Detected stuck categorization for session {session_id}, resetting...")
                
                # Reset the session
                session.categorization_started_at = None
                session.categorization_total = 0
                session.categorization_completed = 0
                session.categorization_failed = 0
                
                # Reset stuck files
                db.query(UploadedFile).filter(
                    UploadedFile.session_id == to_db_id(session_id),
                    UploadedFile.categorization_status == CategorizationStatus.IN_PROGRESS
                ).update({
                    "categorization_status": CategorizationStatus.PENDING,
                    "categorization_started_at": None
                }, synchronize_session=False)
                db.commit()
                
                logger.info("✅ Reset stuck categorization process")
            else:
                logger.info(f"⏳ Categorization already in progress ({time_since_start:.0f}s elapsed)")
        
        # Start background processing with new pipeline
        # The new pipeline handles both cases: with or without headers
        logger.info("🚀 Starting background processing with new lattice pipeline")
        background_tasks.add_task(
            process_with_lattice,
            session_id,
            lattice_headers.headers if lattice_headers else None,
            lattice_headers.headers_by_category if lattice_headers else None
        )
        
        response = {
            "session_id": session_id,
            "status": "started",
            "has_lattice": lattice_headers is not None,
            "poll_endpoint": f"/process/{session_id}/status",
            "categorization_status": {
                "completed": categorized_files,
                "pending": pending_categorization
            }
        }
        
        logger.info(f"✅ API: Processing started successfully for session {session_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"💥 API: Error starting processing for session {session_id}: {str(e)}")
        logger.error("Full error details", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}/status")
async def get_processing_status(
    session_id: str, 
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get the current status of processing for a session including lattice progress.
    Use this endpoint to poll for progress.
    
    Args:
        session_id: The upload session ID
        db: Database session
    
    Returns:
        Comprehensive processing status including categorization and lattice progress
    """
    logger.debug(f"🔍 API: Status request for session {session_id}")
    
    try:
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            logger.error(f"❌ Session {session_id} not found")
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get detailed file status for categorization
        files_query = db.query(UploadedFile).filter(
            UploadedFile.session_id == to_db_id(session_id),
            UploadedFile.bucket_key.isnot(None)
        )
        
        # Count by categorization status
        categorization_status_counts = {
            "pending": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.PENDING).count(),
            "in_progress": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.IN_PROGRESS).count(),
            "completed": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.COMPLETED).count(),
            "failed": files_query.filter(UploadedFile.categorization_status == CategorizationStatus.FAILED).count()
        }
        
        total_files = sum(categorization_status_counts.values())
        
        # Count by category
        category_counts = {}
        for category in DueDiligenceCategory:
            count = files_query.filter(UploadedFile.due_diligence_category == category).count()
            if count > 0:
                category_counts[category.value] = count
        
        # Determine overall categorization status
        if session.categorization_completed_at:
            categorization_overall_status = "completed"
        elif session.categorization_started_at:
            categorization_overall_status = "in_progress"
        else:
            categorization_overall_status = "not_started"
        
        # Calculate categorization progress percentage
        categorization_progress_pct = 0
        if total_files > 0:
            categorization_progress_pct = ((categorization_status_counts["completed"] + categorization_status_counts["failed"]) / total_files) * 100
        
        # Build categorization response
        categorization_info = {
            "status": categorization_overall_status,
            "progress_percentage": round(categorization_progress_pct, 2),
            "started_at": session.categorization_started_at.isoformat() if session.categorization_started_at else None,
            "completed_at": session.categorization_completed_at.isoformat() if session.categorization_completed_at else None,
            "file_status_counts": categorization_status_counts,
            "category_distribution": category_counts,
            "total_files": total_files
        }
        
        # Add categorization duration if applicable
        if session.categorization_started_at:
            end_time = session.categorization_completed_at or datetime.now(timezone.utc)
            duration = (end_time - session.categorization_started_at).total_seconds()
            categorization_info["duration_seconds"] = round(duration, 2)
        
        # Get lattice information if applicable
        lattice_info = None
        if hasattr(session, 'lattice_status') and session.lattice_status:
            logger.debug(f"📊 Building lattice status for session {session_id}")
            
            # Get lattice matrix
            matrix_data = await get_lattice_matrix(session_id, db)
            
            lattice_info = {
                "status": session.lattice_status.value if session.lattice_status else "pending",
                "started_at": session.lattice_started_at.isoformat() if session.lattice_started_at else None,
                "completed_at": session.lattice_completed_at.isoformat() if session.lattice_completed_at else None,
                "progress": session.lattice_progress if session.lattice_progress else {},
                "matrix": matrix_data["matrix"],
                "matrix_summary": {
                    "file_count": matrix_data["file_count"],
                    "header_count": matrix_data["header_count"],
                    "total_cells": matrix_data["total_cells"],
                    "headers": matrix_data["headers"]
                },
                "error_message": session.lattice_error_message
            }
            
            # Add lattice duration if applicable
            if session.lattice_started_at:
                end_time = session.lattice_completed_at or datetime.now(timezone.utc)
                duration = (end_time - session.lattice_started_at).total_seconds()
                lattice_info["duration_seconds"] = round(duration, 2)
            
            # Log lattice metrics for monitoring
            if lattice_info["progress"]:
                completed = lattice_info["progress"].get("completed_cells", 0)
                total = lattice_info["progress"].get("total_cells", 0)
                if total > 0:
                    logger.debug(f"📊 Lattice progress: {completed}/{total} cells ({(completed/total)*100:.1f}%)")
        
        response = {
            "session_id": session_id,
            "categorization": categorization_info,
            "lattice": lattice_info,
            "overall_status": "completed" if (categorization_overall_status == "completed" and 
                                           (not lattice_info or lattice_info["status"] == "completed")) else "in_progress"
        }
        
        logger.debug(f"✅ Status response built for session {session_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API: Error getting status for session {session_id}: {str(e)}")
        logger.error("Full error details", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{session_id}/lattice")
async def get_lattice_matrix_endpoint(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get the current lattice matrix for a session.
    
    Args:
        session_id: The upload session ID
        db: Database session
    
    Returns:
        Complete lattice matrix with metadata
    """
    logger.debug(f"🔍 API: Lattice matrix request for session {session_id}")
    
    try:
        # Validate session exists
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            logger.error(f"❌ Session {session_id} not found")
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get lattice matrix
        matrix_data = await get_lattice_matrix(session_id, db)
        
        # Add session metadata
        response = {
            "session_id": session_id,
            "lattice_status": session.lattice_status.value if hasattr(session, 'lattice_status') and session.lattice_status else "pending",
            "lattice_progress": session.lattice_progress if hasattr(session, 'lattice_progress') and session.lattice_progress else {},
            **matrix_data
        }
        
        logger.debug(f"✅ Lattice matrix response built for session {session_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API: Error getting lattice matrix for session {session_id}: {str(e)}")
        logger.error("Full error details", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Background task for processing with lattice
async def process_with_lattice(
    session_id: str,
    general_headers: Optional[List[str]],
    category_headers: Optional[Dict[str, List[str]]]
):
    """
    Background task for complete processing workflow including lattice analysis
    
    Args:
        session_id: The upload session ID
        general_headers: General headers that apply to all documents
        category_headers: Category-specific headers (ignored in new pipeline)
    """
    logger.info(f"🔧 Background: Starting complete processing for session {session_id}")
    
    from api.models.database import SessionLocal
    db = SessionLocal()
    
    try:
        # For new pipeline, we skip categorization and go directly to lattice analysis
        # The new pipeline handles header extraction if not provided
        logger.info("🔄 Background: Starting new lattice pipeline")
        
        # Use the new service - pass only general headers
        # The new pipeline doesn't use category-specific headers
        await new_lattice_service.analyze_session_with_new_pipeline(
            session_id=session_id,
            provided_headers=general_headers,
            db=db
        )
        
        logger.info("✅ Background: New lattice pipeline completed")
        logger.info("🎉 Background: Complete processing finished successfully")
        
    except Exception as e:
        logger.error(f"💥 Background: Processing failed for session {session_id}: {str(e)}")
        logger.error("Background task error details", exc_info=True)
    finally:
        db.close()
        logger.debug(f"🔧 Background: Database connection closed for session {session_id}")


# Legacy endpoints for backward compatibility
@router.get("/{session_id}")
async def get_session_summary(
    session_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get a summary of files in the session with their categorization results.
    Legacy endpoint for backward compatibility.
    """
    logger.debug(f"🔍 API: Legacy session summary request for session {session_id}")
    
    # Redirect to the comprehensive status endpoint
    status_response = await get_processing_status(session_id, db)
    
    # Transform to legacy format
    categorization = status_response["categorization"]
    
    return {
        "session_id": session_id,
        "total_files": categorization["total_files"],
        "categorization_complete": categorization["status"] == "completed",
        "category_summary": categorization["category_distribution"],
        "files": []  # Could populate this if needed for backward compatibility
    }


@router.get("/{session_id}/category/{category}")
async def get_files_by_category(
    session_id: str,
    category: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get all files in a session that belong to a specific due diligence category.
    """
    logger.debug(f"🔍 API: Files by category request for session {session_id}, category {category}")
    
    try:
        # Validate category
        try:
            dd_category = DueDiligenceCategory(category)
        except ValueError:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid category. Must be one of: {', '.join([c.value for c in DueDiligenceCategory])}"
            )
        
        # Get session
        session = db.query(UploadSession).filter(
            UploadSession.id == to_db_id(session_id)
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Get files by category
        files = db.query(UploadedFile).filter(
            UploadedFile.session_id == to_db_id(session_id),
            UploadedFile.due_diligence_category == dd_category,
            UploadedFile.bucket_key.isnot(None)  # Only uploaded files
        ).all()
        
        files_info = []
        for file in files:
            file_data = {
                "filename": file.filename,
                "file_size": file.file_size,
                "content_type": file.content_type,
                "bucket_key": file.bucket_key,
                "categorization_confidence": float(file.categorization_confidence) if file.categorization_confidence else 0.0,
                "categorized_at": file.categorized_at.isoformat() if file.categorized_at else None
            }
            
            # Include insights if available
            if file.agent_insights:
                file_data["insights"] = file.agent_insights
            
            files_info.append(file_data)
        
        logger.debug(f"✅ Found {len(files_info)} files in category {category}")
        
        return {
            "session_id": session_id,
            "category": category,
            "total_files": len(files_info),
            "files": files_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API: Error getting files by category for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))