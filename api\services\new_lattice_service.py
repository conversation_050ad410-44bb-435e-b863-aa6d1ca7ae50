import asyncio
import time
import json
import os
import shutil
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

from api.models.database import (
    UploadedFile, UploadSession, <PERSON>tticeStatus, <PERSON>tticeResult, 
    to_db_id, from_db_id
)
from api.services.pdf_extractor import pdf_extractor
from api.utils.logger import logger

# Import the new pipeline components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from agents_v2.latticeheader_agent.base import extract_lattice_headers
from agents_v2.query_engine.main import run_parallel_lattice_analysis


class NewLatticeService:
    """Service for new RAG-based lattice analysis with header generation"""
    
    def __init__(self):
        self.pdf_extractor = pdf_extractor
        logger.info("🔬 Initialized NewLatticeService with RAG pipeline")
    
    async def analyze_session_with_new_pipeline(
        self,
        session_id: str,
        provided_headers: Optional[List[str]] = None,
        db: Session = None
    ):
        """
        Main orchestrator for new lattice pipeline
        
        Args:
            session_id: The upload session ID
            provided_headers: Optional headers provided by user
            db: Database session
        """
        from api.models.database import SessionLocal
        
        should_close_db = False
        if db is None:
            db = SessionLocal()
            should_close_db = True
        
        start_time = time.time()
        logger.info(f"🚀 Starting new lattice pipeline for session {session_id}")
        
        try:
            # Get session
            session = db.query(UploadSession).filter(
                UploadSession.id == to_db_id(session_id)
            ).first()
            
            if not session:
                logger.error(f"❌ Session {session_id} not found")
                raise ValueError(f"Session {session_id} not found")
            
            # Update session status
            session.lattice_status = LatticeStatus.IN_PROGRESS
            session.lattice_started_at = datetime.now(timezone.utc)
            session.lattice_progress = {"stage": "initialization", "progress": 0}
            db.commit()
            
            # Get all uploaded files
            files = db.query(UploadedFile).filter(
                UploadedFile.session_id == to_db_id(session_id),
                UploadedFile.bucket_key.isnot(None)
            ).all()
            
            logger.info(f"📁 Found {len(files)} files for analysis")
            
            if not files:
                session.lattice_status = LatticeStatus.COMPLETED
                session.lattice_completed_at = datetime.now(timezone.utc)
                db.commit()
                return
            
            # Prepare file paths by downloading from S3 to temp directory
            import tempfile
            temp_dir = tempfile.mkdtemp(prefix="lattice_")
            pdf_paths = []
            file_map = {}  # Map path to file object
            
            for file in files:
                try:
                    # Download file from S3
                    logger.info(f"📥 Downloading {file.filename} from S3")
                    pdf_content = self.pdf_extractor._download_pdf_from_s3(file.bucket_key)
                    
                    if pdf_content:
                        # Save to temp file
                        temp_path = os.path.join(temp_dir, file.filename)
                        with open(temp_path, 'wb') as f:
                            f.write(pdf_content)
                        
                        pdf_paths.append(temp_path)
                        file_map[temp_path] = file
                        logger.info(f"✅ Downloaded {file.filename} to {temp_path}")
                    else:
                        logger.warning(f"⚠️ Failed to download {file.filename}")
                except Exception as e:
                    logger.error(f"❌ Error downloading {file.filename}: {str(e)}")
            
            # Phase 1: Determine headers
            if provided_headers:
                logger.info(f"📋 Using provided {len(provided_headers)} headers")
                lattice_headers = provided_headers
                
                # Update progress
                session.lattice_progress = {
                    "stage": "headers_provided", 
                    "headers": lattice_headers,
                    "progress": 10
                }
                db.commit()
            else:
                logger.info("🔍 No headers provided - extracting from documents")
                
                # Update progress
                session.lattice_progress = {
                    "stage": "extracting_headers",
                    "progress": 5
                }
                db.commit()
                
                # Extract headers using the new pipeline
                header_results = await extract_lattice_headers(pdf_paths)
                
                if header_results["final_headers_result"]["success"]:
                    lattice_headers = header_results["final_headers_result"]["final_headers"]
                    logger.info(f"✅ Extracted {len(lattice_headers)} headers")
                    
                    # Store extracted headers in session metadata
                    session.lattice_progress = {
                        "stage": "headers_extracted",
                        "headers": lattice_headers,
                        "header_extraction_results": header_results,
                        "progress": 20
                    }
                    db.commit()
                else:
                    raise ValueError("Failed to extract headers from documents")
            
            # Phase 2: Run RAG analysis with headers
            logger.info("🤖 Starting RAG analysis with lattice headers")
            
            session.lattice_progress = {
                "stage": "running_rag_analysis",
                "headers": lattice_headers,
                "progress": 30
            }
            db.commit()
            
            # Run the parallel lattice analysis
            rag_results = await run_parallel_lattice_analysis(
                pdf_paths=pdf_paths,
                lattice_headers=lattice_headers,
                top_k=5,
                rrf=True
            )
            
            # Phase 3: Store results in database
            logger.info("💾 Storing results in database")
            
            session.lattice_progress = {
                "stage": "storing_results",
                "progress": 80
            }
            db.commit()
            
            # Store each result
            total_cells = len(rag_results) * len(lattice_headers)
            stored_cells = 0
            
            for doc_name, doc_results in rag_results.items():
                # Find the corresponding file
                file_obj = None
                for path, file in file_map.items():
                    # Match by filename since doc_name comes from the PDF processor
                    if Path(path).name == doc_name or file.filename == doc_name:
                        file_obj = file
                        break
                
                if not file_obj:
                    logger.warning(f"⚠️ Could not find file object for {doc_name}")
                    continue
                
                for header, answer_data in doc_results.items():
                    # Extract the answer text
                    if isinstance(answer_data, dict):
                        answer_text = answer_data.get('answer', str(answer_data))
                    else:
                        answer_text = str(answer_data)
                    
                    # Create lattice result
                    result = LatticeResult(
                        session_id=to_db_id(session_id),
                        file_id=file_obj.id,
                        header_key=header,
                        result_value=answer_text[:500],  # Truncate if too long
                        result_type="rag_extracted",
                        agent_type="hybrid_rag",
                        processed_at=datetime.now(timezone.utc)
                    )
                    db.add(result)
                    
                    stored_cells += 1
                    if stored_cells % 10 == 0:
                        # Update progress periodically
                        progress = 80 + (20 * stored_cells / total_cells)
                        session.lattice_progress = {
                            "stage": "storing_results",
                            "progress": int(progress),
                            "cells_stored": stored_cells,
                            "total_cells": total_cells
                        }
                        db.commit()
            
            # Final commit
            session.lattice_status = LatticeStatus.COMPLETED
            session.lattice_completed_at = datetime.now(timezone.utc)
            session.lattice_progress = {
                "stage": "completed",
                "progress": 100,
                "total_files": len(files),
                "total_headers": len(lattice_headers),
                "total_cells": stored_cells
            }
            db.commit()
            
            elapsed_time = time.time() - start_time
            logger.info(f"✅ Lattice analysis completed in {elapsed_time:.2f}s")
            logger.info(f"📊 Processed {len(files)} files × {len(lattice_headers)} headers = {stored_cells} cells")
            
        except Exception as e:
            logger.error(f"❌ Error in lattice pipeline: {str(e)}", exc_info=True)
            
            # Update session with error
            session.lattice_status = LatticeStatus.COMPLETED
            session.lattice_completed_at = datetime.now(timezone.utc)
            session.lattice_error_message = str(e)
            session.lattice_progress = {
                "stage": "error",
                "error": str(e)
            }
            db.commit()
            
            raise
        
        finally:
            # Cleanup temp directory
            if 'temp_dir' in locals():
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"🧹 Cleaned up temp directory: {temp_dir}")
                except Exception as e:
                    logger.warning(f"Failed to cleanup temp directory: {str(e)}")
            
            if should_close_db:
                db.close()


# Create singleton instance
new_lattice_service = NewLatticeService() 