import os
import json
import faiss  # Kept for potential future use - currently using Qdrant instead
import requests
import numpy as np
import time
import hashlib
import asyncio
import aiofiles
import base64
import uuid  # Added for UUID generation
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from nltk.tokenize import word_tokenize
from rank_bm25 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from openai import OpenAI
from dotenv import load_dotenv
from typing import List, Dict, Tuple
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct

# Download required NLTK data
import nltk
try:
    # Try to find punkt first
    nltk.data.find('tokenizers/punkt')
except LookupError:
    print("Downloading NLTK punkt tokenizer...")
    nltk.download('punkt', quiet=True)
    print("✅ NLTK punkt tokenizer downloaded successfully")

# Simple tokenization function that doesn't rely on NLTK data
def simple_tokenize(text):
    """Simple word tokenization that splits on whitespace and punctuation."""
    import re
    # Split on whitespace and common punctuation
    tokens = re.findall(r'\b\w+\b', text.lower())
    return tokens

# Use simple tokenization instead of NLTK
word_tokenize = simple_tokenize

# Load environment variables
load_dotenv()

# Configuration
JINA_API_URL = os.getenv("JINA_API_URL", "https://api.jina.ai/v1/embeddings")
JINA_API_KEY = os.getenv("JINA_API_KEY")
MODEL_NAME = os.getenv("MODEL_NAME")

# Qdrant Configuration
QDRANT_URL = os.getenv("QDRANT_URL", "https://3312ccd1-6af5-4b15-8e3e-60ddbdd7b38a.europe-west3-0.gcp.cloud.qdrant.io:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
QDRANT_COLLECTION_NAME = os.getenv("QDRANT_COLLECTION_NAME", "document_chunks")

# Initialize OpenAI client
client = OpenAI(
    base_url=os.getenv("AGENTIC_BASE_URL"),
    api_key=os.getenv("AGENTIC_API_KEY")
)

# Initialize Qdrant client
qdrant_client = QdrantClient(
    url=QDRANT_URL,
    api_key=QDRANT_API_KEY,
)

# Cache directory for parsed PDFs 
CACHE_DIR = Path(__file__).parent.parent / "cached_data"
CACHE_DIR.mkdir(exist_ok=True)



### --------------------- PDF PARSING & CACHING ---------------------

def get_file_hash(file_path: str) -> str:
    """Generate hash for a file to use as cache key."""
    with open(file_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def get_cache_path(pdf_path: str) -> Path:
    """Get cache file path for a PDF."""
    file_hash = get_file_hash(pdf_path)
    filename = Path(pdf_path).stem
    return CACHE_DIR / f"{filename}_{file_hash}.txt"

async def get_cached_content(pdf_path: str) -> str:
    """Get cached content if available."""
    cache_path = get_cache_path(pdf_path)
    if cache_path.exists():
        print(f"✓ Using cached content for {Path(pdf_path).name}")
        async with aiofiles.open(cache_path, 'r', encoding='utf-8') as f:
            return await f.read()
    return None

async def cache_content(pdf_path: str, content: str):
    """Cache the parsed content."""
    cache_path = get_cache_path(pdf_path)
    async with aiofiles.open(cache_path, 'w', encoding='utf-8') as f:
        await f.write(content)
    print(f"✓ Cached content for {Path(pdf_path).name}")

def parse_pdf_with_jina(pdf_path: str, max_retries: int = 3) -> str:
    """Parse entire PDF using JINA AI Reader API with retry logic."""
    print(f"📄 Parsing {Path(pdf_path).name} with JINA...")
    
    try:
        with open(pdf_path, 'rb') as f:
            pdf_bytes = f.read()
    except Exception as e:
        raise ValueError(f"Failed to read PDF file: {str(e)}")
    
    # Check file size
    file_size_mb = len(pdf_bytes) / (1024 * 1024)
    if file_size_mb > 50:  # 50MB limit
        print(f"⚠️  Large file detected ({file_size_mb:.1f}MB) - this may take longer")
    
    # Encode to base64
    pdf_base64 = base64.b64encode(pdf_bytes).decode('utf-8')
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'QuanteraHybridRAG/1.0'
    }
    
    # Add API key if available
    jina_api_key = os.getenv("JINA_API_KEY")
    if jina_api_key:
        headers["Authorization"] = f"Bearer {jina_api_key}"
    
    # Prepare payload for entire document
    payload = {
        "url": None,
        "pdf": pdf_base64,
        "filename": Path(pdf_path).name
    }
    
    for attempt in range(max_retries):
        try:
            timeout = min(300, max(60, int(file_size_mb * 10)))
            
            print(f"🔄 Attempt {attempt + 1}/{max_retries} (timeout: {timeout}s)")
            
            # Make API request
            response = requests.post(
                "https://r.jina.ai/",
                json=payload,
                headers=headers,
                timeout=timeout
            )
            
            if response.status_code == 200:
                content = response.text.strip()
                
                # Check if we got a valid response
                if content and len(content) > 50 and "Access denied" not in content and "Cloudflare" not in content:
                    print(f"✓ Successfully parsed {Path(pdf_path).name} ({len(content)} characters)")
                    return content
                else:
                    raise ValueError("Invalid or empty response from JINA API")
            
            elif response.status_code == 503:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1 
                    print(f"⚠️  JINA API temporarily unavailable (503). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API persistently unavailable: {response.status_code} - {response.text}")
            
            elif response.status_code == 429:
                
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * 3  
                    print(f"⚠️  Rate limited (429). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API rate limit exceeded: {response.status_code} - {response.text}")
            
            elif response.status_code in [502, 504]:
                # Bad gateway or gateway timeout
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 2 
                    print(f"⚠️  Gateway error ({response.status_code}). Retrying in {wait_time}s...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ValueError(f"JINA API gateway error: {response.status_code} - {response.text}")
            
            else:
        
                raise ValueError(f"JINA API error: {response.status_code} - {response.text}")
        
        except requests.exceptions.RequestException as e:
            # Network/connection errors
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Connection error: {str(e)}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Network error after {max_retries} attempts: {str(e)}")
        
        except Exception as e:
            # Other unexpected errors
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) + 1
                print(f"⚠️  Unexpected error: {str(e)}. Retrying in {wait_time}s...")
                time.sleep(wait_time)
                continue
            else:
                raise ValueError(f"Failed to parse PDF after {max_retries} attempts: {str(e)}")
    
    raise ValueError(f"Failed to parse PDF with JINA after {max_retries} attempts")

async def get_or_parse_pdf_content(pdf_path: str) -> str:
    """Get PDF content from cache or parse with JINA."""
    # Check cache first
    cached_content = await get_cached_content(pdf_path)
    if cached_content:
        return cached_content
    
    # Parse with JINA in thread pool
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor() as executor:
        content = await loop.run_in_executor(executor, parse_pdf_with_jina, pdf_path)
    
    # Cache the result
    await cache_content(pdf_path, content)
    return content

### --------------------- CHUNKING ---------------------

def calculate_tokens(text: str) -> int:
    """Calculate approximate tokens using 4 characters = 1 token."""
    return len(text) // 4

def chunk_text(text: str, max_tokens: int = 7000) -> list:
    """
    Chunk text into segments of maximum token count.
    Uses simple sentence-based chunking with overlap.
    """
    print(f"Chunking text of {len(text)} characters (~{calculate_tokens(text)} tokens)")
    
    max_chars = max_tokens * 4  
    
    sentences = text.split('. ')
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        sentence_with_period = sentence + '. '
        
        # Check if adding this sentence would exceed limit
        if calculate_tokens(current_chunk + sentence_with_period) > max_tokens:
            if current_chunk:  # Only add non-empty chunks
                chunks.append(current_chunk.strip())
                current_chunk = sentence_with_period
            else:
                if len(sentence_with_period) > max_chars:
                    for i in range(0, len(sentence_with_period), max_chars):
                        chunk_part = sentence_with_period[i:i + max_chars]
                        if chunk_part.strip():
                            chunks.append(chunk_part.strip())
                else:
                    current_chunk = sentence_with_period
        else:
            current_chunk += sentence_with_period
    
    if current_chunk.strip():
        chunks.append(current_chunk.strip())
    
    print(f"Created {len(chunks)} chunks")
    
    chunk_sizes = [calculate_tokens(chunk) for chunk in chunks]
    print(f"Chunk token sizes - Min: {min(chunk_sizes)}, Max: {max(chunk_sizes)}, Avg: {sum(chunk_sizes)/len(chunk_sizes):.1f}")
    
    return chunks

### --------------------- DOCUMENT LOADING ---------------------

async def load_and_process_pdf(pdf_path: str) -> tuple:
    """
    Load a PDF, parse it, and chunk it into manageable pieces.
    Returns chunks and chunk metadata.
    """
    print(f"Loading and processing PDF: {pdf_path}")
    
    # Validate PDF path
    if not Path(pdf_path).exists():
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    
    # Get or parse PDF content
    content = await get_or_parse_pdf_content(pdf_path)
    
    # Chunk the content
    chunks = chunk_text(content)
    
    # Create chunk IDs
    chunk_ids = [f"chunk_{i}" for i in range(len(chunks))]
    
    # Create document metadata
    document_info = {
        "file_name": Path(pdf_path).name,
        "file_path": pdf_path,
        "total_chunks": len(chunks),
        "total_content_length": len(content),
        "total_tokens": calculate_tokens(content)
    }
    
    print(f"Successfully processed {document_info['file_name']}: {len(chunks)} chunks, ~{document_info['total_tokens']} tokens")
    
    return chunks, chunk_ids, document_info

### --------------------- EMBEDDINGS ---------------------

def get_jina_embeddings(texts, batch_size=1000, max_retries=3, delay=2):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {JINA_API_KEY}"
    }
    
    all_embeddings = []
    total_batches = (len(texts) + batch_size - 1) // batch_size
    
    print(f"Processing {len(texts)} texts in {total_batches} batches")
    
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i + batch_size]
        batch_num = i // batch_size + 1
        
        print(f"Processing batch {batch_num}/{total_batches} ({len(batch_texts)} texts)")
        
        # Retry mechanism for API calls
        for attempt in range(max_retries):
            try:
                data = {
                    "model": "jina-embeddings-v3",
                    "task": "text-matching",
                    "input": batch_texts
                }
                
                response = requests.post(JINA_API_URL, headers=headers, json=data)
                
                if response.status_code == 200:
                    batch_embeddings = response.json()["data"]
                    all_embeddings.extend([e["embedding"] for e in batch_embeddings])
                    
                    print(f"Successfully processed batch {batch_num}/{total_batches}")
                    break
                else:
                    print(f"API request failed with status {response.status_code}")
                    print(f"Response: {response.text}")
                    if attempt < max_retries - 1:
                        print(f"Retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                        delay *= 2  
                    else:
                        response.raise_for_status()
                        
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"Exception occurred: {e}")
                    print(f"Retrying in {delay} seconds (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
                    delay *= 2  
                else:
                    raise
    
    print(f"Successfully got embeddings for {len(all_embeddings)} texts")
    return np.array(all_embeddings).astype("float32")

### --------------------- INDEXERS ---------------------

def get_embedding_dimension() -> int:
    """Get the actual embedding dimension from JINA API."""
    try:
        # Test with a single text to get the embedding dimension
        test_embedding = get_jina_embeddings(["test"])
        return test_embedding.shape[1]
    except Exception as e:
        print(f"Warning: Could not determine embedding dimension, using default 1024: {e}")
        return 1024

def build_dense_index(chunks):
    print("Building dense (Qdrant) index with Jina embeddings")
    
    # Get the actual embedding dimension
    embedding_dim = get_embedding_dimension()
    print(f"Using embedding dimension: {embedding_dim}")
    
    # Create collection if it doesn't exist
    try:
        collection_info = qdrant_client.get_collection(QDRANT_COLLECTION_NAME)
        existing_dim = collection_info.config.params.vectors.size
        if existing_dim != embedding_dim:
            print(f"⚠️ Collection '{QDRANT_COLLECTION_NAME}' exists with dimension {existing_dim}, but need {embedding_dim}")
            print(f"🗑️ Deleting existing collection and recreating...")
            qdrant_client.delete_collection(collection_name=QDRANT_COLLECTION_NAME)
            qdrant_client.create_collection(
                collection_name=QDRANT_COLLECTION_NAME,
                vectors_config=VectorParams(size=embedding_dim, distance=Distance.COSINE)
            )
            print(f"✅ Collection '{QDRANT_COLLECTION_NAME}' recreated with dimension {embedding_dim}")
        else:
            print(f"Collection '{QDRANT_COLLECTION_NAME}' already exists with correct dimension {embedding_dim}")
    except Exception as e:
        print(f"Collection '{QDRANT_COLLECTION_NAME}' not found or error occurred. Creating new one.")
        try:
            qdrant_client.delete_collection(collection_name=QDRANT_COLLECTION_NAME)
        except:
            pass
        qdrant_client.create_collection(
            collection_name=QDRANT_COLLECTION_NAME,
            vectors_config=VectorParams(size=embedding_dim, distance=Distance.COSINE)
        )
        print(f"Collection '{QDRANT_COLLECTION_NAME}' created successfully.")

    # Prepare points for Qdrant with UUIDs
    points = []
    uuid_to_index = {}  # Mapping UUID to chunk index
    for i, chunk in enumerate(chunks):
        chunk_uuid = str(uuid.uuid4())
        uuid_to_index[chunk_uuid] = i
        point_payload = {"text": chunk, "index": i}
        points.append(PointStruct(id=chunk_uuid, vector=get_jina_embeddings([chunk])[0], payload=point_payload))

    # Add points to Qdrant
    qdrant_client.upsert(collection_name=QDRANT_COLLECTION_NAME, points=points)
    print(f"Dense index built successfully with {len(points)} vectors in collection '{QDRANT_COLLECTION_NAME}'")
    
    # Store the UUID mapping in the client for later use
    qdrant_client.uuid_to_index = uuid_to_index
    return qdrant_client

# FAISS version (commented out for potential future use)
# def build_dense_index_faiss(chunks):
#     print("Building dense (FAISS) index with Jina embeddings")
#     
#     embeddings = get_jina_embeddings(chunks)
#     
#     dim = embeddings.shape[1]
#     index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
#     ids = np.arange(len(chunks))
#     
#     index.add_with_ids(embeddings, ids)
#     print(f"Dense index built successfully with {index.ntotal} vectors")
#     
#     return index

def build_sparse_index(chunks):
    print("Building sparse (BM25) index")
    
    tokenized = [word_tokenize(chunk.lower()) for chunk in chunks]
    
    bm25 = BM25Okapi(tokenized)
    print(f"Sparse index built successfully with {len(tokenized)} documents")
    
    return bm25

### --------------------- HYBRID QUERY ---------------------

def hybrid_query(query, bm25, qdrant_client, chunks, top_k=5, rrf=False):
    print(f"Processing query: {query}")

    # Sparse BM25 search
    tokenized_query = word_tokenize(query.lower())
    bm25_scores = bm25.get_scores(tokenized_query)
    bm25_top_ids = np.argsort(bm25_scores)[::-1][:top_k]

    # Dense Qdrant search
    query_vec = get_jina_embeddings([query])
    search_result = qdrant_client.search(
        collection_name=QDRANT_COLLECTION_NAME,
        query_vector=query_vec[0].tolist(),
        limit=top_k
    )
    # Convert UUIDs back to indices using the mapping
    qdrant_top_ids = [qdrant_client.uuid_to_index.get(point.id, -1) for point in search_result]
    qdrant_top_ids = [idx for idx in qdrant_top_ids if idx >= 0]  # Filter out invalid indices

   
    if rrf:
        print("Performing Reciprocal Rank Fusion")
        # Get rankings for BM25
        bm25_ranks = {idx: 1/(rank + 60) for rank, idx in enumerate(bm25_top_ids)}
        
        # Get rankings for Qdrant
        qdrant_ranks = {idx: 1/(rank + 60) for rank, idx in enumerate(qdrant_top_ids)}
        
        # Combine all document IDs
        all_ids = set(bm25_ranks.keys()).union(set(qdrant_ranks.keys()))
        
        # Calculate RRF scores
        rrf_scores = {}
        for doc_id in all_ids:
            rrf_scores[doc_id] = bm25_ranks.get(doc_id, 0) + qdrant_ranks.get(doc_id, 0)
        
        # Sort by RRF scores and get top_k IDs
        combined_ids = sorted(rrf_scores.keys(), key=lambda x: rrf_scores[x], reverse=True)[:top_k]
        print(f"RRF fusion completed with {len(combined_ids)} results")
    else:
        combined_ids = set(bm25_top_ids).union(set(qdrant_top_ids))


    results = []
    for idx in combined_ids:
        if idx < len(chunks):
            results.append((idx, chunks[idx]))

    print(f"Retrieved {len(results)} chunks for query")
    return results

# FAISS version (commented out for potential future use)
# def hybrid_query_faiss(query, bm25, faiss_index, chunks, top_k=5, rrf=False):
#     print(f"Processing query: {query}")
# 
#     # Sparse BM25 search
#     tokenized_query = word_tokenize(query.lower())
#     bm25_scores = bm25.get_scores(tokenized_query)
#     bm25_top_ids = np.argsort(bm25_scores)[::-1][:top_k]
# 
#     # Dense FAISS search
#     query_vec = get_jina_embeddings([query])
#     _, faiss_top_ids = faiss_index.search(query_vec, top_k)
# 
#    
#     if rrf:
#         print("Performing Reciprocal Rank Fusion")
#         # Get rankings for BM25
#         bm25_ranks = {idx: 1/(rank + 60) for rank, idx in enumerate(bm25_top_ids)}
#         
#         # Get rankings for FAISS
#         faiss_ranks = {idx: 1/(rank + 60) for rank, idx in enumerate(faiss_top_ids[0])}
#         
#         # Combine all document IDs
#         all_ids = set(bm25_ranks.keys()).union(set(faiss_ranks.keys()))
#         
#         # Calculate RRF scores
#         rrf_scores = {}
#         for doc_id in all_ids:
#             rrf_scores[doc_id] = bm25_ranks.get(doc_id, 0) + faiss_ranks.get(doc_id, 0)
#         
#         # Sort by RRF scores and get top_k IDs
#         combined_ids = sorted(rrf_scores.keys(), key=lambda x: rrf_scores[x], reverse=True)[:top_k]
#         print(f"RRF fusion completed with {len(combined_ids)} results")
#     else:
#         combined_ids = set(bm25_top_ids).union(set(faiss_top_ids[0]))
# 
# 
#     results = []
#     for idx in combined_ids:
#         if idx < len(chunks):
#             results.append((idx, chunks[idx]))
# 
#     print(f"Retrieved {len(results)} chunks for query")
#     return results

### --------------------- GENERATION ---------------------

def generate_answer(query, retrieved_chunks, model="agentic-large"):
    print("Generating answer using LLM")
    
    # Format context from retrieved chunks
    context = "\n\n".join([chunk for _, chunk in retrieved_chunks])
    context_length = len(context)
    
    messages = [
        {"role": "system", "content": "You are a helpful assistant. Answer questions based on the provided context."},
        {"role": "user", "content": f"""Context: {context}\n\nQuestion: {query}\n\nPlease provide a clear and concise answer based on the context above."""}
    ]

    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.7,
            max_tokens=5000
        )
        
        answer = response.choices[0].message.content
        print(f"Answer generated successfully (length: {len(answer)} characters)")
        
        return answer
        
    except Exception as e:
        print(f"Error generating answer: {str(e)}")
        return "Sorry, I encountered an error generating the answer."

### --------------------- PARALLEL DOCUMENT PROCESSING ---------------------

class DocumentProcessor:
    """Class to hold document processing state and methods."""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.chunks = None
        self.qdrant_client = None  # Changed from faiss_index
        self.bm25_index = None
        self.document_info = None
        self.uuid_to_index: Dict[str, int] = {}  # Mapping UUID to chunk index
    
    async def process_document(self):
        """Process a single document: parse, chunk, and build indices."""
        print(f"\n🔄 Processing document: {Path(self.pdf_path).name}")
        
        # Load and process the PDF
        self.chunks, _, self.document_info = await load_and_process_pdf(self.pdf_path)
        
        # Build indices
        self.qdrant_client = build_dense_index(self.chunks)  # Changed from faiss_index
        self.bm25_index = build_sparse_index(self.chunks)
        
        print(f"✅ Document processed: {self.document_info['file_name']}")
        return self
    
    def query_document(self, query: str, top_k: int = 5, rrf: bool = False):
        """Query this document with a specific lattice header."""
        if not self.chunks or not self.qdrant_client or not self.bm25_index:
            raise ValueError("Document not processed yet")
        
        return hybrid_query(query, self.bm25_index, self.qdrant_client, self.chunks, top_k, rrf)

# FAISS version (commented out for potential future use)
# class DocumentProcessorFAISS:
#     """Class to hold document processing state and methods using FAISS."""
#     
#     def __init__(self, pdf_path: str):
#         self.pdf_path = pdf_path
#         self.chunks = None
#         self.faiss_index = None
#         self.bm25_index = None
#         self.document_info = None
#     
#     async def process_document(self):
#         """Process a single document: parse, chunk, and build indices."""
#         print(f"\n🔄 Processing document: {Path(self.pdf_path).name}")
#         
#         # Load and process the PDF
#         self.chunks, _, self.document_info = await load_and_process_pdf(self.pdf_path)
#         
#         # Build indices
#         self.faiss_index = build_dense_index_faiss(self.chunks)
#         self.bm25_index = build_sparse_index(self.chunks)
#         
#         print(f"✅ Document processed: {self.document_info['file_name']}")
#         return self
#     
#     def query_document(self, query: str, top_k: int = 5, rrf: bool = False):
#         """Query this document with a specific lattice header."""
#         if not self.chunks or not self.faiss_index or not self.bm25_index:
#             raise ValueError("Document not processed yet")
#         
#         return hybrid_query_faiss(query, self.bm25_index, self.faiss_index, self.chunks, top_k, rrf)

async def process_documents_parallel(pdf_paths: List[str]) -> List[DocumentProcessor]:
    """Process multiple documents in parallel."""
    print(f"\n🚀 Starting parallel processing of {len(pdf_paths)} documents")
    
    processors = [DocumentProcessor(pdf_path) for pdf_path in pdf_paths]
    
    tasks = [processor.process_document() for processor in processors]
    processed_processors = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful_processors = []
    for i, result in enumerate(processed_processors):
        if isinstance(result, Exception):
            print(f"❌ Failed to process {Path(pdf_paths[i]).name}: {result}")
        else:
            successful_processors.append(result)
    
    print(f"✅ Successfully processed {len(successful_processors)}/{len(pdf_paths)} documents")
    return successful_processors

### --------------------- PARALLEL QUERYING ---------------------

async def query_document_parallel(processor: DocumentProcessor, lattice_headers: List[str], 
                                top_k: int = 5, rrf: bool = False) -> Dict[str, str]:
    """Query a single document with all lattice headers in parallel."""
    print(f"\n🔍 Querying document: {processor.document_info['file_name']}")
    
    # Create tasks for each lattice header
    async def query_single_header(header: str) -> Tuple[str, str]:
        print(f"  Querying: {header}")
        
        # Get relevant chunks
        retrieved_chunks = processor.query_document(header, top_k, rrf)
        
        # Generate answer
        answer = generate_answer(header, retrieved_chunks)
        
        return header, answer
    
    # Run all queries in parallel
    tasks = [query_single_header(header) for header in lattice_headers]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results
    document_answers = {}
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"  ❌ Failed to query '{lattice_headers[i]}': {result}")
            document_answers[lattice_headers[i]] = f"Error: {str(result)}"
        else:
            header, answer = result
            document_answers[header] = answer
            print(f"  ✅ Generated answer for: {header}")
    
    return document_answers

async def process_all_documents_parallel(processors: List[DocumentProcessor], 
                                       lattice_headers: List[str],
                                       top_k: int = 5, rrf: bool = False) -> Dict[str, Dict[str, str]]:
    """Process all documents with all lattice headers in parallel."""
    print(f"\n🎯 Starting parallel querying of {len(processors)} documents with {len(lattice_headers)} headers")
    
    # Create tasks for each document
    tasks = [query_document_parallel(processor, lattice_headers, top_k, rrf) 
             for processor in processors]
    
    # Run all document queries in parallel
    all_results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results
    final_results = {}
    for i, result in enumerate(all_results):
        if isinstance(result, Exception):
            print(f"❌ Failed to process document {processors[i].document_info['file_name']}: {result}")
            final_results[processors[i].document_info['file_name']] = {
                header: f"Error: {str(result)}" for header in lattice_headers
            }
        else:
            final_results[processors[i].document_info['file_name']] = result
    
    return final_results

### --------------------- RESULTS PROCESSING ---------------------

def create_results_string(results: Dict[str, Dict[str, str]]) -> str:
    """Convert results to a formatted string."""
    result_string = "LATTICE ANALYSIS RESULTS\n"
    result_string += "=" * 60 + "\n\n"
    
    for doc_name, answers in results.items():
        result_string += f"📄 DOCUMENT: {doc_name}\n"
        result_string += "-" * 40 + "\n"
        
        for header, answer in answers.items():
            result_string += f"🎯 {header}:\n"
            result_string += f"   {answer}\n\n"
        
        result_string += "\n"
    
    return result_string

def save_results_to_file(results: Dict[str, Dict[str, str]], output_path: str = "lattice_results.txt"):
    """Save results to text file."""
    result_string = create_results_string(results)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(result_string)
    
    print(f"📊 Results saved to: {output_path}")
    return result_string

### --------------------- MAIN PIPELINE ---------------------

async def run_hybrid_rag_pipeline(pdf_path: str):
    """
    Initialize hybrid RAG pipeline with a single PDF document.
    """
    print("Starting hybrid RAG pipeline initialization")
    
    # Load and process the PDF
    chunks, document_info = await load_and_process_pdf(pdf_path)
    
    print(f"Loaded {len(chunks)} chunks from {document_info['file_name']}")
    print(f"Document stats: {document_info['total_content_length']} chars, ~{document_info['total_tokens']} tokens")

    # Build indices
    qdrant_client = build_dense_index(chunks)  # Changed from faiss_index
    bm25_index = build_sparse_index(chunks)

    print("RAG pipeline initialization completed successfully")
    
    return lambda query: hybrid_query(query, bm25_index, qdrant_client, chunks), document_info

# FAISS version (commented out for potential future use)
# async def run_hybrid_rag_pipeline_faiss(pdf_path: str):
#     """
#     Initialize hybrid RAG pipeline with a single PDF document using FAISS.
#     """
#     print("Starting hybrid RAG pipeline initialization (FAISS)")
#     
#     # Load and process the PDF
#     chunks, document_info = await load_and_process_pdf(pdf_path)
#     
#     print(f"Loaded {len(chunks)} chunks from {document_info['file_name']}")
#     print(f"Document stats: {document_info['total_content_length']} chars, ~{document_info['total_tokens']} tokens")
# 
#     # Build indices
#     faiss_index = build_dense_index_faiss(chunks)
#     bm25_index = build_sparse_index(chunks)
# 
#     print("RAG pipeline initialization completed successfully")
#     
#     return lambda query: hybrid_query_faiss(query, bm25_index, faiss_index, chunks), document_info

async def run_parallel_lattice_analysis(pdf_paths: List[str], 
                                      lattice_headers: List[str] = None,
                                      top_k: int = 5, 
                                      rrf: bool = False,
                                      save_results: bool = True) -> Dict[str, Dict[str, str]]:
    """
    Main pipeline for parallel lattice analysis across multiple documents.
    
    Args:
        pdf_paths: List of PDF file paths to process
        lattice_headers: List of lattice headers to query (uses defaults if None)
        top_k: Number of top chunks to retrieve
        rrf: Whether to use Reciprocal Rank Fusion
        save_results: Whether to save results to file
    
    Returns:
        Dictionary with document names as keys and header-answer pairs as values
    """
    
    
    print(f"\n{'='*60}")
    print(f" PARALLEL LATTICE ANALYSIS PIPELINE")
    print(f"{'='*60}")
    print(f"📄 Documents to process: {len(pdf_paths)}")
    print(f"🎯 Lattice headers: {len(lattice_headers)}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # Step 1: Process all documents in parallel
        print(f"\n📋 STEP 1: Processing {len(pdf_paths)} documents in parallel...")
        processors = await process_documents_parallel(pdf_paths)
        
        if not processors:
            raise ValueError("No documents were successfully processed")
        
        # Step 2: Query all documents with all lattice headers in parallel
        print(f"\n🔍 STEP 2: Querying {len(processors)} documents with {len(lattice_headers)} headers in parallel...")
        results = await process_all_documents_parallel(processors, lattice_headers, top_k, rrf)
        
        # Step 3: Process and save results
        print(f"\n📊 STEP 3: Processing results...")
        if save_results:
            result_string = save_results_to_file(results)
            print(f"📈 Results summary:")
            print(f"   - Documents processed: {len(results)}")
            print(f"   - Headers per document: {len(lattice_headers)}")
            print(f"   - Total answers generated: {len(results) * len(lattice_headers)}")
        
    
        
        return results
        
    except Exception as e:
        print(f"\n❌ Pipeline failed: {str(e)}")
        raise

### --------------------- ENTRY POINT ---------------------

async def main():
    """Main async entry point for the RAG system."""
    print("Starting RAG chatbot session")
    
    
    pdf_paths = [
        "mp_materials/pdfs/form-10-k.pdf",
        "mp_materials/pdfs/form-10-q.pdf",
        "mp_materials/pdfs/employment-agreement.pdf"
    ]
    
    # lattice headers
    lattice_headers = [
        "Key Financial Metrics",
        "Total Assets", 
    ]
    
    try:
        # Run parallel lattice analysis
        results = await run_parallel_lattice_analysis(
            pdf_paths=pdf_paths,
            lattice_headers=lattice_headers,
            top_k=5,
            rrf=True,
            save_results=True
        )
        
        # Display results summary
        print(f"\n📋 RESULTS SUMMARY:")
        print(f"{'='*60}")
        for doc_name, answers in results.items():
            print(f"\n📄 {doc_name}:")
            for header, answer in answers.items():
                print(f"  🎯 {header}: {answer[:100]}...")
        
        print(f"\n🎉 Analysis completed successfully!")
        
    except Exception as e:
        print(f"❌ Error in main pipeline: {e}")
        return

if __name__ == "__main__":
    asyncio.run(main()) 
